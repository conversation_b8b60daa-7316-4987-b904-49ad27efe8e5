

<?php 

session_start();

require 'connection.php';

require_once('database.php');

require_once('library.php');

require_once('connection_inventory.php');
require_once('database_inventory.php');

isUser();
check_session();
    $userid=$_SESSION['desgn'];

 $a=$_SESSION['username'];

 date_default_timezone_set('Asia/Kolkata');



$date = date('Y/m/d h:i:s', time());

$teamname=$_POST['teamname'] ?? '';

$createDate=$date;$user=$_SESSION['UserId'] ?? '';

$ab1=$_POST['FollDate'] ?? '';

 $date1 = date('Y-m-d h:i:s',  strtotime($ab1));

 /*$sql="select * from login where type='$a'";

$result = mysqli_query($con, $sql);

$row = mysqli_fetch_array($result, MYSQLI_BOTH);

   $userid=$row['rid'];*/



$sql="select * from `tbl_courier_officers` where cid='$userid'";

$result = mysqli_query($con, $sql);

$row1 = mysqli_fetch_array($result, MYSQLI_BOTH);

  $uoffice=$row1['office']; $uaddress=$row1['address']; 

 $clrkname=$row1['Manager_name'];

 



//	$sql = "SELECT * FROM `custreg` inner join city on custreg.custcity=city.ctid where userid='$userid' ORDER BY custname ASC";





	$sql = "SELECT * FROM `custreg` inner join city on custreg.custcity=city.ctid ORDER BY custname ASC";



$result2=mysqli_query($con,$sql);

$company = $_POST['company'] ?? '';
$drop1 = $_POST['drop1'] ?? '';
$custname = $_POST['custname'] ?? '';

// Add search option as the first option after placeholder
$drop1 = $drop1 . "<option value='__SEARCH__'>🔍 Search...</option>";

while($row2=mysqli_fetch_array($result2))

{

	if($company==$row2['custid'])

	{

	$drop1=$drop1."<option value='".$row2['custid']."' selected>".$row2['custcode']."-".$row2['custname']."-".$row2['cityname']."</option>";

	}

	else{

	 $drop1=$drop1."<option value='".$row2['custid']."' >".$row2['custcode']."-".$row2['custname']."-".$row2['cityname']."</option>";

	}

}



if($a!="admin") {

 $sql1 = "SELECT * FROM `receiver_reg` inner join tbl_city_code on receiver_reg.receivercity=tbl_city_code.Id  ORDER BY receiver_code ASC";

}

else{

		 $sql1 = "SELECT * FROM `receiver_reg` inner join tbl_city_code on receiver_reg.receivercity=tbl_city_code.Id ORDER BY receiver_code ASC";

}

$result3=mysqli_query($con,$sql1);

$company1 = $_POST['company1'] ?? '';
$drop2 = $_POST['drop2'] ?? '';
$company1 = $_POST['company1'] ?? '';

// Add search option as the first option after placeholder
$drop2 = $drop2 . "<option value='__SEARCH__'>🔍 Search...</option>";

while($row3=mysqli_fetch_array($result3))

{

	if($company1==$row3['id'])

	{

	$drop2=$drop2."<option value='".$row3['id']."'  selected>".$row3['receiver_code']."-".$row3['receivername']."-".$row3['city_name']."</option>";

	}

	else{

	 $drop2=$drop2."<option value='".$row3['id']."' >".$row3['receiver_code']."-".$row3['receivername']."-".$row3['city_name']."</option>";

	}

}



 $statesql="SELECT * FROM state order by statename ASC";

 $stateresult=mysqli_query($con,$statesql);

 $stname = $_POST['stname'] ?? '';
 $statedrop = $_POST['statedrop'] ?? '';

 while($staterow=mysqli_fetch_array($stateresult))

{

	if($stname==$staterow['stid'])

	{

	 $statedrop=$statedrop."<option value='".$staterow['stid']."' selected>".$staterow['statename']."</option>";

	}

	else{

	  $statedrop=$statedrop."<option value='".$staterow['stid']."' >".$staterow['statename']."</option>";

	}

}

  

//echo $Bookingmod = $_GET['Bookingmode'];



 $query = "SELECT * FROM `tbl_courier` WHERE `cons_no` REGEXP '^[A-Z]' ORDER BY cid DESC LIMIT 1";  

$result = mysqli_query($con,$query);

//$consno ='A1002700';

if (mysqli_num_rows($result) != NULL)

{

   while ($row=mysqli_fetch_array($result))

{

     

 $consno=$row['cons_no'];

//$consno ='A10067460';

$cons= ++$consno;

}



} 

else{

   

 $cons='V100001';



}

	





	 $statesql="SELECT * FROM tbl_city_code";

 $cityresult=mysqli_query($con,$statesql);

 $cityname = $_POST['cityname'] ?? '';
 $citydrop = $_POST['citydrop'] ?? '';

 // Add search option as the first option after placeholder
 $citydrop = $citydrop . "<option value='__SEARCH__'>🔍 Search...</option>";

 while($staterow=mysqli_fetch_array($cityresult))

{

	if($cityname==$staterow['Id'])

	{

	 $citydrop=$citydrop."<option value='".$staterow['Id']."' selected>".$staterow['city_name']."-".$staterow['city_code']."</option>";

	}

	else{

	  $citydrop=$citydrop."<option value='".$staterow['Id']."' >".$staterow['city_name']."-".$staterow['city_code']."</option>";

	}

}



$partDropdown = '';

// Query to fetch distinct part numbers
$partQuery = "SELECT DISTINCT part_no FROM inward_material WHERE part_no IS NOT NULL AND part_no != ''";
$partResult = mysqli_query($conInventory, $partQuery);

// Optional: get the selected part_no from POST (if any)
$selectedPartNo = $_POST['part_no'] ?? '';

// Add search option as the first option after placeholder
$partDropdown .= "<option value='__SEARCH__'>🔍 Search...</option>";

// Loop through the result and build the <option> tags
while ($row = mysqli_fetch_assoc($partResult)) {
    $partNo = htmlspecialchars($row['part_no']); // Safe output
    if ($selectedPartNo === $partNo) {
        $partDropdown .= "<option value='$partNo' selected>$partNo</option>";
    } else {
        $partDropdown .= "<option value='$partNo'>$partNo</option>";
    }
}





$query = "SELECT MAX(cons_no) AS cons FROM tbl_courier";

$count = 0; // Initialize count variable

if($result = mysqli_query($con,$query))

{

while ($row = mysqli_fetch_assoc($result))

{

 $counter = $row['cons'];

 $counter++;

 $count = $counter; // Set count for use in success messages

}

}

	





?>

<!DOCTYPE html>

<!--[if lt IE 7 ]><html class="ie ie6" lang="en"> <![endif]-->

<!--[if IE 7 ]><html class="ie ie7" lang="en"> <![endif]-->

<!--[if IE 8 ]><html class="ie ie8" lang="en"> <![endif]-->

<!--[if (gte IE 9)|!(IE)]><!--><html lang="en"> <!--<![endif]-->

<head>



	<!-- Basic Page Needs

  ================================================== -->

	<meta charset="utf-8">

	<title>Vivanta Logistics</title>

	<meta name="description" content="">

	<meta name="author" content="">

	<!-- Mobile Specific Metas

  ================================================== -->

	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">

	<!-- CSS

  ================================================== -->

	<link rel="stylesheet" href="css/bootstrap.min.css" media="screen">

	<!-- jquery ui css -->

	<link rel="stylesheet" href="css/jquery-ui-1.10.1.min.css">

	<link rel="stylesheet" href="css/customize.css">

	<link rel="stylesheet" href="css/font-awesome.css">

	<link rel="stylesheet" href="css/style.css">

	<!-- flexslider css-->

	<link rel="stylesheet" href="css/select2.min.css" />

	<!-- Select2 CSS from CDN as backup -->
	<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />

	<link rel="stylesheet" href="css/flexslider.css">

	<!-- Custom CSS for dropdown search -->
	<style>
	.dropdown-search-wrapper {
		position: relative;
		margin-bottom: 10px;
	}

	.dropdown-search-input {
		border: 1px solid #ccc;
		border-radius: 4px;
		padding: 6px 12px;
		font-size: 14px;
		width: 100%;
		box-sizing: border-box;
	}

	.dropdown-search-input:focus {
		border-color: #66afe9;
		outline: 0;
		box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px rgba(102,175,233,.6);
	}

	/* Enhanced Select2 custom styling */
	.select2-container {
		width: 100% !important;
	}

	.select2-selection {
		height: 34px !important;
		border: 1px solid #ccc !important;
		border-radius: 4px !important;
	}

	.select2-selection__rendered {
		line-height: 32px !important;
		padding-left: 12px !important;
	}

	.select2-selection__arrow {
		height: 32px !important;
	}

	/* Dropdown styling */
	.select2-dropdown {
		border: 1px solid #ccc !important;
		border-radius: 4px !important;
	}

	.select2-search__field {
		border: 1px solid #ccc !important;
		border-radius: 4px !important;
		padding: 6px 12px !important;
	}

	.select2-results__option {
		padding: 8px 12px !important;
	}

	.select2-results__option--highlighted {
		background-color: #337ab7 !important;
		color: white !important;
	}

	/* Make sure dropdowns are wide enough */
	.select2-container--default .select2-selection--single {
		min-width: 200px;
	}
	</style>

	<!-- fancybox -->

	<link rel="stylesheet" href="js/fancybox/jquery.fancybox.css">

	<script src="https://ajax.googleapis.com/ajax/libs/jquery/1.12.4/jquery.min.js">



	<!--[if lt IE 9]>

		<script src="https://html5shim.googlecode.com/svn/trunk/html5.js"></script>

		<script src="https://ie7-js.googlecode.com/svn/version/2.1(beta4)/IE9.js"></script>

		<link rel="stylesheet" href="css/font-awesome-ie7.css">-->

	<![endif]-->

	<!-- Favicons

	================================================== -->

	<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.3.1/jquery.min.js">

</script>

		<!-- Select2 JavaScript -->
		<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

<script type="text/JavaScript">



function MM_findObj(n, d) { //v4.01

  var p,i,x;  if(!d) d=document; if((p=n.indexOf("?"))>0&&parent.frames.length) {

    d=parent.frames[n.substring(p+1)].document; n=n.substring(0,p);}

  if(!(x=d[n])&&d.all) x=d.all[n]; for (i=0;!x&&i<d.forms.length;i++) x=d.forms[i][n];

  for(i=0;!x&&d.layers&&i<d.layers.length;i++) x=MM_findObj(n,d.layers[i].document);

  if(!x && d.getElementById) x=d.getElementById(n); return x;

}



function MM_validateForm() { //v4.0

  var i,p,q,nm,test,num,min,max,errors='',args=MM_validateForm.arguments;

  for (i=0; i<(args.length-2); i+=3) { test=args[i+2]; val=MM_findObj(args[i]);

    if (val) { nm=val.name; if ((val=val.value)!="") {

      if (test.indexOf('isEmail')!=-1) { p=val.indexOf('@');

        if (p<1 || p==(val.length-1)) errors+='- '+nm+' must contain an e-mail address.\n';

      } else if (test!='R') { num = parseFloat(val);

        if (isNaN(val)) errors+='- '+nm+' must contain a number.\n';

        if (test.indexOf('inRange') != -1) { p=test.indexOf(':');

          min=test.substring(8,p); max=test.substring(p+1);

          if (num<min || max<num) errors+='- '+nm+' must contain a number between '+min+' and '+max+'.\n';

    } } } else if (test.charAt(0) == 'R') errors += '- '+nm+' is required.\n'; }

  } if (errors) alert('The following error(s) occurred:\n'+errors);

  document.MM_returnValue = (errors == '');

}





function fun2()

{

var a=document.getElementById("rccode").value;

//alert(a);

obj=new XMLHttpRequest();

obj.open("GET","ajaxgetrcDetails.php?a="+a,true);

obj.send();

obj.onreadystatechange=funca1

}

function fun()

{

var a=document.getElementById("sname").value;

//alert(a);

obj=new XMLHttpRequest();

obj.open("GET","ajaxgetCustDetails.php?a="+a,true);

obj.send();

obj.onreadystatechange=funca

}

function funca()

{

   if(obj.readyState==4)

     {

	vala=obj.responseText;

	//alert(vala);

	var res = vala.split("*");

		//alert(res[0]);

		document.getElementById("Shippername").value=res[1];

		document.getElementById("Shipperphone").value=res[2];

		document.getElementById("Shipperemail").value=res[3];

		document.getElementById("Shipperadd").value=res[4];

		document.getElementById("custzip").value=res[5];

	//	document.getElementById("custin").value=res[8];

	//	document.getElementById("cstin").value=res[9];

		document.getElementById("custgst").value=res[10];

		document.getElementById("custpan").value=res[11];

		document.getElementById("uoffice").value=res[4];

		

		

		 

		

		}	

}



function funca1()

{

   if(obj.readyState==4)

     {

	vala=obj.responseText;

	var res = vala.split("*");

	document.getElementById("Receivername").value=res[3];

		document.getElementById("Receiveradd").value=res[6];

		document.getElementById("Receiverphone").value=res[4];

		document.getElementById("Receiveremail").value=res[5];

		document.getElementById("rzip").value=res[7];

		document.getElementById("gstno").value=res[10];

		document.getElementById("rcpan").value=res[11];

		document.getElementById("desti").value=res[6];

			

	 

		

		}	

}

function fun1()

{ alert("hiii"); }

// Function to fetch part details from inward_material table
function fetchPartDetails(partNo, selectElement) {
    if (!partNo) {
        // Clear the fields if no part is selected
        var row = selectElement.closest('.outward-row-flex');
        if (row) {
            row.querySelector('input[name="existing_packages[]"]').value = '';
            row.querySelector('input[name="existing_quantity[]"]').value = '';
        }
        return;
    }

    // Create AJAX request
    var xhr = new XMLHttpRequest();
    xhr.open('POST', 'ajax_getPartDetails.php', true);
    xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');

    xhr.onreadystatechange = function() {
        if (xhr.readyState === 4 && xhr.status === 200) {
            try {
                var response = JSON.parse(xhr.responseText);

                // Find the row containing this select element
                var row = selectElement.closest('.outward-row-flex');
                if (row) {
                    if (response.error) {
                        console.error('Error fetching part details:', response.error);
                        // Clear fields on error
                        row.querySelector('input[name="existing_packages[]"]').value = '';
                        row.querySelector('input[name="existing_quantity[]"]').value = '';
                    } else {
                        // Populate the existing packages and quantity fields
                        row.querySelector('input[name="existing_packages[]"]').value = response.noofpackages || '';
                        row.querySelector('input[name="existing_quantity[]"]').value = response.qty_invoice || '';
                    }
                }
            } catch (e) {
                console.error('Error parsing response:', e);
                console.error('Response text:', xhr.responseText);
            }
        }
    };

    // Send the request with part number
    xhr.send('part_no=' + encodeURIComponent(partNo));
}

// Function to handle shipment type change
function handleShipmentTypeChange() {
    var shipmentType = document.querySelector('input[name="shipmentType"]:checked');
    var transferDiv = document.getElementById('transferDiv');

    if (shipmentType && shipmentType.value === 'transfer') {
        transferDiv.style.display = 'block';
    } else {
        transferDiv.style.display = 'none';
        // Reset transfer type when hiding
        document.getElementById('transferTypes').value = '';
        handleTransferTypeChange();
    }
}

// Function to handle transfer type change
function handleTransferTypeChange() {
    var transferType = document.getElementById('transferTypes').value;
    var inwardSection = document.getElementById('transferInwardSection');
    var outwardSection = document.getElementById('transferOutwardSection');

    if (transferType === 'inward') {
        inwardSection.style.display = 'block';
        outwardSection.style.display = 'none';
    } else if (transferType === 'outward') {
        inwardSection.style.display = 'none';
        outwardSection.style.display = 'block';
    } else {
        inwardSection.style.display = 'none';
        outwardSection.style.display = 'none';
    }
}

// Function to add new transfer row (for inward)
function addTransferRow() {
    var container = document.getElementById('transferRowsContainer');
    var newRow = document.createElement('div');
    newRow.className = 'transfer-row-flex';
    newRow.style.cssText = 'display: flex; gap: 10px; align-items: center; margin-bottom: 10px;';

    newRow.innerHTML = `
        <label style="min-width: 80px;"></label>
        <input type="text" name="transfer_part_no[]" placeholder="Part No" style="width: 200px;">
        <input type="text" name="transfer_quantity[]" placeholder="Quantity" style="width: 200px;">
        <input type="text" name="transfer_packages[]" placeholder="No. of Packages" style="width: 200px;">
        <button type="button" onclick="removeTransferRow(this)" style="background-color: #dc3545; color: white; border: none; padding: 5px 10px; border-radius: 3px;">Remove</button>
    `;

    container.appendChild(newRow);
}

// Function to add new outward row
function addOutwardRow() {
    console.log('=== Starting addOutwardRow function ===');

    var container = document.getElementById('outwardRowsContainer');
    if (!container) {
        console.error('Container outwardRowsContainer not found!');
        return;
    }
    console.log('Container found:', container);

    // Get the original select element to clone its options
    var originalSelect = document.querySelector('select[name="outward_part_no[]"]');
    if (!originalSelect) {
        console.error('Could not find original select element with name="outward_part_no[]"');
        // Let's try to find any select in the outward section
        var outwardSection = document.getElementById('transferOutwardSection');
        if (outwardSection) {
            originalSelect = outwardSection.querySelector('select');
            console.log('Found select in outward section:', originalSelect);
        }
        if (!originalSelect) {
            console.error('No select element found at all!');
            return;
        }
    }

    console.log('Original select found with', originalSelect.options.length, 'options');
    console.log('Original select options:', Array.from(originalSelect.options).map(opt => opt.value + ' - ' + opt.text));

    // Create new row div
    var newRow = document.createElement('div');
    newRow.className = 'outward-row-flex';
    newRow.style.cssText = 'display: flex; gap: 10px; align-items: center; margin-bottom: 10px;';

    // Create elements step by step instead of using innerHTML
    var label = document.createElement('label');
    label.style.minWidth = '80px';
    label.textContent = '';

    var select = document.createElement('select');
    select.name = 'outward_part_no[]';
    select.style.width = '130px';
    select.className = 'outward-part-select select2-search';

    // Clone options from original select
    console.log('Cloning options...');
    for (var i = 0; i < originalSelect.options.length; i++) {
        var originalOption = originalSelect.options[i];
        var newOption = document.createElement('option');
        newOption.value = originalOption.value;
        newOption.textContent = originalOption.textContent;
        select.appendChild(newOption);
        console.log('Cloned option:', newOption.value, '-', newOption.textContent);
    }

    var input1 = document.createElement('input');
    input1.type = 'text';
    input1.name = 'existing_packages[]';
    input1.placeholder = 'Existing Packages';
    input1.style.width = '130px';
    input1.readOnly = true;

    var input2 = document.createElement('input');
    input2.type = 'text';
    input2.name = 'outward_packages[]';
    input2.placeholder = 'New Packages';
    input2.style.width = '130px';

    var input3 = document.createElement('input');
    input3.type = 'text';
    input3.name = 'existing_quantity[]';
    input3.placeholder = 'Existing Quantity';
    input3.style.width = '130px';
    input3.readOnly = true;

    var input4 = document.createElement('input');
    input4.type = 'text';
    input4.name = 'outward_quantity[]';
    input4.placeholder = 'New Quantity';
    input4.style.width = '130px';

    var button = document.createElement('button');
    button.type = 'button';
    button.textContent = 'Remove';
    button.style.cssText = 'background-color: #dc3545; color: white; border: none; padding: 5px 10px; border-radius: 3px;';
    button.onclick = function() { removeOutwardRow(this); };

    // Append all elements to the row
    newRow.appendChild(label);
    newRow.appendChild(select);
    newRow.appendChild(input1);
    newRow.appendChild(input2);
    newRow.appendChild(input3);
    newRow.appendChild(input4);
    newRow.appendChild(button);

    // Add the row to container
    container.appendChild(newRow);

    // Attach event listener to the select
    select.addEventListener('change', handleOutwardPartChange);
    console.log('Event listener attached to new select');
    console.log('New select has', select.options.length, 'options');

    // Setup search option for the new select element
    setupSearchOption($(select));

    console.log('=== addOutwardRow completed successfully ===');
}

// Function to remove transfer row
function removeTransferRow(button) {
    button.parentElement.remove();
}

// Function to remove outward row
function removeOutwardRow(button) {
    button.parentElement.remove();
}

// Function to fetch part details for regular Part No field
function fetchRegularPartDetails(partNo) {
    if (!partNo) {
        // Clear the packages field if no part is selected
        document.getElementById('noofpackages').value = '';
        return;
    }

    // Create AJAX request
    var xhr = new XMLHttpRequest();
    xhr.open('POST', 'ajax_getPartDetails.php', true);
    xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');

    xhr.onreadystatechange = function() {
        if (xhr.readyState === 4 && xhr.status === 200) {
            try {
                var response = JSON.parse(xhr.responseText);

                if (response.error) {
                    console.error('Error fetching part details:', response.error);
                    // Clear field on error
                    document.getElementById('noofpackages').value = '';
                } else {
                    // Populate the packages field with existing packages from inward_material
                    document.getElementById('noofpackages').value = response.noofpackages || '';
                }
            } catch (e) {
                console.error('Error parsing response:', e);
                console.error('Response text:', xhr.responseText);
            }
        }
    };

    // Send the request with part number
    xhr.send('part_no=' + encodeURIComponent(partNo));
}

// Function to initialize outward part selects (called on page load)
function initializeOutwardSelects() {
    // Find all outward part selects and ensure they have event listeners
    var outwardSelects = document.querySelectorAll('select[name="outward_part_no[]"]');
    outwardSelects.forEach(function(select) {
        // Remove any existing event listeners and add new one
        select.removeEventListener('change', handleOutwardPartChange);
        select.addEventListener('change', handleOutwardPartChange);
    });
}

// Event handler for outward part select changes
function handleOutwardPartChange(event) {
    console.log('Outward part changed:', event.target.value);
    fetchPartDetails(event.target.value, event.target);
}

// Store part options globally for dynamic row creation
var partOptionsHtml = '';
var partNumbers = <?php
    // Create JavaScript array of part numbers
    $partArray = array();
    $partQuery = "SELECT DISTINCT part_no FROM inward_material WHERE part_no IS NOT NULL AND part_no != ''";
    $partResult = mysqli_query($conInventory, $partQuery);
    while ($row = mysqli_fetch_assoc($partResult)) {
        $partArray[] = htmlspecialchars($row['part_no']);
    }
    echo json_encode($partArray);
?>;

// Initialize when page loads
document.addEventListener('DOMContentLoaded', function() {
    // Debug: Check if part numbers are loaded
    console.log('Part numbers loaded:', partNumbers);
    console.log('Number of parts:', partNumbers ? partNumbers.length : 0);

    // Store the part options from the first select for later use
    var firstSelect = document.querySelector('select[name="outward_part_no[]"]');
    if (firstSelect) {
        partOptionsHtml = firstSelect.innerHTML;
        console.log('Stored part options for dynamic rows');
    }

    initializeOutwardSelects();

    // Initialize Select2 for all dropdowns with search functionality
    setTimeout(function() {
        initializeSelect2Dropdowns();
    }, 500); // Small delay to ensure DOM is fully loaded
});

// Function to setup search option for a select element
function setupSearchOption($select) {
    var selectName = $select.attr('name') || $select.attr('id') || 'unnamed';

    // Store original options (excluding placeholder and search option)
    var originalOptions = [];
    $select.find('option').each(function() {
        var value = $(this).val();
        var text = $(this).text();
        if (value && value !== '__SEARCH__' && text !== '-- Please Select --' && text !== '-- Select--' && text !== '--Select Booking Type--' && text !== '-- Select Type --') {
            originalOptions.push({
                value: value,
                text: text,
                selected: $(this).is(':selected')
            });
        }
    });
    $select.data('original-options', originalOptions);

    // Initialize Select2
    $select.select2({
        placeholder: "-- Please Select --",
        allowClear: true,
        width: '100%'
    });

    // Handle selection change
    $select.off('change.searchOption').on('change.searchOption', function() {
        var selectedValue = $(this).val();

        if (selectedValue === '__SEARCH__') {
            // Show search modal/input
            showSearchModal($select, originalOptions);

            // Reset selection to placeholder
            setTimeout(function() {
                $select.val('').trigger('change');
            }, 100);
        }
    });
}

// Function to initialize Select2 for all dropdowns
function initializeSelect2Dropdowns() {
    console.log('Starting search option initialization...');

    // Check if jQuery and Select2 are loaded
    if (typeof $ === 'undefined') {
        console.error('jQuery is not loaded!');
        return;
    }

    if (typeof $.fn.select2 === 'undefined') {
        console.error('Select2 is not loaded!');
        return;
    }

    // Setup search option for all dropdowns
    $('.select2-search').each(function() {
        setupSearchOption($(this));
    });

    console.log('Search option initialization completed for all dropdowns');
}

// Function to reinitialize Select2 for dynamically added elements
function reinitializeSelect2(element) {
    if (element && element.length > 0) {
        setupSearchOption(element);
    }
}

</script>

  <script>

 $(function() {



            $( "#datepicker" ).datepicker({dateFormat: 'yy-mm-dd'});

  });

$(function() {



 $( "#datepicker1" ).datepicker({dateFormat: 'yy-mm-dd'});

  });

  // SEARCH OPTION IMPLEMENTATION - Initialize dropdowns with search functionality
  $(document).ready(function() {
      console.log('Initializing dropdowns with search functionality...');

      // Check if Select2 is loaded
      if (typeof $.fn.select2 === 'undefined') {
          console.error('Select2 is not loaded!');
          return;
      }

      // Wait for page to load completely
      setTimeout(function() {
          $('.select2-search').each(function() {
              var $select = $(this);
              var selectName = $select.attr('name') || $select.attr('id') || 'unnamed';

              console.log('Setting up search for:', selectName);

              // Setup search functionality
              setupSearchOption($select);

              console.log('✓ Search setup complete for:', selectName);
          });

          console.log('All dropdowns initialized with search functionality!');
      }, 1000);
  });

  // Function to show search modal
  function showSearchModal($select, originalOptions) {
      var selectName = $select.attr('name') || $select.attr('id') || 'dropdown';

      // Create modal HTML
      var modalHtml = `
          <div id="searchModal" style="
              position: fixed;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
              background: rgba(0,0,0,0.5);
              z-index: 9999;
              display: flex;
              align-items: center;
              justify-content: center;
          ">
              <div style="
                  background: white;
                  padding: 20px;
                  border-radius: 8px;
                  width: 90%;
                  max-width: 500px;
                  max-height: 80vh;
                  overflow-y: auto;
                  box-shadow: 0 4px 20px rgba(0,0,0,0.3);
              ">
                  <h3 style="margin-top: 0; color: #333;">Search in ${selectName}</h3>
                  <input type="text" id="searchInput" placeholder="Type to search anywhere in text..." style="
                      width: 100%;
                      padding: 10px;
                      border: 2px solid #ddd;
                      border-radius: 4px;
                      font-size: 16px;
                      margin-bottom: 15px;
                      box-sizing: border-box;
                  ">
                  <div id="searchResults" style="
                      max-height: 300px;
                      overflow-y: auto;
                      border: 1px solid #ddd;
                      border-radius: 4px;
                      margin-bottom: 15px;
                  "></div>
                  <div style="text-align: right;">
                      <button onclick="closeSearchModal()" style="
                          padding: 8px 16px;
                          background: #6c757d;
                          color: white;
                          border: none;
                          border-radius: 4px;
                          cursor: pointer;
                          margin-right: 10px;
                      ">Cancel</button>
                  </div>
              </div>
          </div>
      `;

      // Remove existing modal if any
      $('#searchModal').remove();

      // Add modal to body
      $('body').append(modalHtml);

      // Focus on search input
      $('#searchInput').focus();

      // Handle search input
      $('#searchInput').on('input', function() {
          var searchTerm = $(this).val().toLowerCase();
          var resultsHtml = '';

          if (searchTerm.length === 0) {
              resultsHtml = '<div style="padding: 10px; color: #666;">Type to search...</div>';
          } else {
              var matchCount = 0;

              $.each(originalOptions, function(index, option) {
                  if (option.value && option.text.toLowerCase().indexOf(searchTerm) > -1) {
                      resultsHtml += `
                          <div class="search-result-item" data-value="${option.value}" style="
                              padding: 10px;
                              border-bottom: 1px solid #eee;
                              cursor: pointer;
                              transition: background-color 0.2s;
                          " onmouseover="this.style.backgroundColor='#f8f9fa'"
                             onmouseout="this.style.backgroundColor='white'">
                              ${option.text}
                          </div>
                      `;
                      matchCount++;
                  }
              });

              if (matchCount === 0) {
                  resultsHtml = '<div style="padding: 10px; color: #666;">No matches found</div>';
              }
          }

          $('#searchResults').html(resultsHtml);

          // Handle result item clicks
          $('.search-result-item').on('click', function() {
              var selectedValue = $(this).data('value');
              var selectedText = $(this).text();

              // Set the selected value in the original select
              $select.val(selectedValue).trigger('change');

              console.log('Selected:', selectedText, 'with value:', selectedValue);

              // Close modal
              closeSearchModal();
          });
      });

      // Initial display
      $('#searchInput').trigger('input');
  }

  // Function to close search modal
  function closeSearchModal() {
      $('#searchModal').remove();
  }

  // Close modal when clicking outside
  $(document).on('click', '#searchModal', function(e) {
      if (e.target.id === 'searchModal') {
          closeSearchModal();
      }
  });

  // Close modal with Escape key
  $(document).on('keydown', function(e) {
      if (e.key === 'Escape') {
          closeSearchModal();
      }
  });

  </script>

  

  

  

  

  

 

  

 <script language="javascript" type="text/javascript" src="datetimepicker.js">   </script>  

</head>

<?php

include("header.php");

?>



			<?php

	$ree = ''; // Initialize ree variable
	$qs=mysqli_query($con,"select cons_no from tbl_courier where cid='$count'");

	while($re=mysqli_fetch_row($qs))

	{

		$ree=$re[0];

	}

if(isset($_SESSION['sav']))

{

	echo "<div align='center'><h3>Courier is added successfully.

							  Your Registration No is: <b style='color:red'>$count </b>& Con no. is : <b style='color:red'>$ree</b></h3></div>";

	echo "<br>";

	unset($_SESSION['sav']);

}

if(isset($_SESSION['b']))

{

	

	echo "<div align='center'><h3>Courier is added successfully.

							  Your Registration No is: <b style='color:red'>$count </b>& Con no. is : <b style='color:red'>$ree</b></h3></div>";

	echo "<br>";

	unset($_SESSION['b']);

}

?>

<div class="container">

                <div class="span12">

					<div class="register">

						

                         <div class="titleHeader clearfix">

		                    	<h3>Book Shipment </h3>

					     </div>

		<!--<form action="process.php?action=add-cons" method="post" class="form-horizontal" name="bookshipment">-->

<form action="saveBillPrint.php" method="post" class="form-horizontal" name="bookshipment">

			 <div class="row">	

				<div class="span6">

						<center><legend>&nbsp;&nbsp;&nbsp;&nbsp;Shipper info :</legend></center>

                

						<div class="control-group">

					         <div class="controls">

                                 

        <label class="control-label"> <input type="radio" name="colorRadio" value="account" onclick="paymentMode(this);" required> Account Party </label>

    <label class="control-label"> <input type="radio" name="colorRadio" value="cash" onclick="paymentMode(this);"  required > Cash Party </label>

                                  

                                  

							</div>

							<div class="clearfix"></div>

                                

	                      <br>
						  
	                      
	                      <br>

	                       <div>

							    <label class="control-label">Shipper Code: <span class="text-error">*</span> </label>

							        <div class="controls">

									    <select  name="sname" id="sname" onchange="fun(this);to(this);" class="select2-search" required>

									     <option onSelect="fun()" value="" selected >-- Please Select --</option>

												<?php echo $drop1; ?>

								        </select>

	                                </div></div>

							     <br>	

							

                    

						

							     

							     

	<div class="control-group">

							   	    

							    <label class="control-label">Payment Mode: <span class="text-error">*</span> </label> 

       						    <div class="controls"> 

	                                <select name="Bookingmode" id="Bookingmode" class="select2-search">



									     </select>

									    

									   

									    

									

	                            </div>	 

							</div>

							     

							   

							

							

							</div>

							   

                             <div class="control-group " id="insurances" >

							    <label class="control-label">Insurance(%): <span class="text-error">*</span></label> 

							        <div class="controls">

										<select name="insuran" id="insuran" onchange="insur(this.value)" class="select2-search">

											<option value="Select" selected="selected">-- Select--</option>

											<option value="__SEARCH__">🔍 Search...</option>

											<option value="ys">Yes</option>

											<option value="no">No</option>

												<option value="carrierisk">Carrier Risk</option>

											<option value="yor">Your Own Risk</option>

										</select>

									  <input type="text" name="insurance" id="insurance" style="display:none" placeholder="Insurance in Percent ">

							    </div>

							</div>

							



						<div class="control-group ">

							    <label class="control-label">Shipper Name : <span class="text-error">*</span></label>

							    <div class="controls">

							      <input type="text" name="Shippername" id="Shippername" placeholder="Shipper Name" value="<?php echo $custname; ?>">

							     <!-- <span class="help-inline"><i class="icon-ok"></i> Avaliable input!</span>-->

							    </div>

							</div><!--end control-group-->

							

							

								<div class="control-group ">

							    <label class="control-label">Shipper Address : <span class="text-error">*</span></label>

							    <div class="controls">

							      <input type="text" name="Shipperadd" id="Shipperadd" placeholder="Shipper Address" value="<?php echo $custname; ?>">

							     <!-- <span class="help-inline"><i class="icon-ok"></i> Avaliable input!</span>-->

							    </div>

							</div>

								<div class="control-group ">

							    <label class="control-label" for="Shipperaddress">Shipper City : <span class="text-error">*</span></label>

							    <div class="controls">

							      <select name="Shipperaddress" id="Shipperaddress" class="select2-search" required><option value="" selected="selected">-- Select--</option>

							     <!--< <span class="help-inline">-->	<?php echo $citydrop; ?>

							     </select>

							    </div>

							</div><!--end control-group   <i class="icon-remove"></i> Invalid input!</span>-->

                            <div class="control-group success">

							    <label class="control-label" for="custzip">Zip Code: </label>

							    <div class="controls">

							      <input type="text" name="custzip"  id="custzip" placeholder="Zip Code">

							      <!--<span class="help-inline"><i class="icon-ok"></i> Avaliable input!</span>-->

							   </div>

							</div>

							<div class="control-group">

							    <label class="control-label" for="Shipperphone">Contact : <span class="text-error">*</span></label>

							    <div class="controls">

							      <input type="text" name="Shipperphone" id="Shipperphone" placeholder="Mobile No">

							    </div>

							</div><!--end control-group-->

							<div class="control-group">

							    <label class="control-label">E-Mail :</label>

							    <div class="controls">

							      <input type="email" name="Shipperemail" id="Shipperemail" placeholder="<EMAIL>">

							    </div>

							</div><!--end control-group-->

						<!--end control-group-->

							

						<!--end control-group-->



                                                        <!--end control-group-->    



							<div class="control-group">

							    <label class="control-label" for="custstax">GST No: </label>

							    <div class="controls">

							      <input type="text" name="custgst" id="custgst" placeholder="GST No" >

							    </div>

							</div><!--end control-group-->

							<!--<div class="control-group ">

							    <label class="control-label" for="custpan">PAN No : </label>

							    <div class="controls">

							      <input type="text" name="custpan" id="custpan" placeholder="PAN No" >

							   

							    </div>

							</div> --> 

							

					 </div>

	

						<center><legend >&nbsp;&nbsp;&nbsp;&nbsp;Receiver info :</legend></center>

                      <div class="span5"> 

                      <div class="control-group ">

		<label class="control-label"> <input type="radio" name="colorRadio" value="walkin" onclick="paymentMode(this);" > Walk In Customer </label>

					

							    

							</div>

							

							 <div class="control-group ">

							 <br>

							    <label class="control-label" for="Receiveraddress">Receiver Code : <span class="text-error">*</span></label>

							    <div class="controls">

							     <select name="rccode" id="rccode" onchange="fun2(this);to(this);" class="select2-search" required>

									     <option onSelect="fun2()" value="" selected>-- Please Select --</option>

												<?php echo $drop2; ?>

								        </select>

							     <!--< <span class="help-inline">-->

							    </div>

							</div>

							

							<div class="control-group success">

							    <label class="control-label" for="Receivername">Receiver Name : <span class="text-error">*</span></label>

							    <div class="controls">

							      <input type="text" name="Receivername" id="Receivername" placeholder="Receiver Name">

							      <!--<span class="help-inline"><i class="icon-ok"></i> Avaliable input!</span>-->

							    </div>

							</div>

							

								<div class="control-group success">

							    <label class="control-label" for="Receivername">Receiver Address : <span class="text-error">*</span></label>

							    <div class="controls">

							      <input type="text" name="Receiveradd" id="Receiveradd" placeholder="Receiver Address">

							      <!--<span class="help-inline"><i class="icon-ok"></i> Avaliable input!</span>-->

							    </div>

							</div>

							

							<div class="control-group ">

							    <label class="control-label" for="Receiveraddress">Receiver City : <span class="text-error">*</span></label>

							    <div class="controls">

							       <select name="Receiveraddress" id="Receiveraddress" class="select2-search"required><option value="" selected="selected">-- Select--</option>

							     <!--< <span class="help-inline">-->	<?php echo $citydrop; ?>

							     </select>
							   

							       

							
							    </div>

							</div><!--end control-group-->

							<div class="control-group">

							    <label class="control-label" for="Receiverphone">Contact No: <span class="text-error">*</span></label>

							    <div class="controls">

							      <input type="text" name="Receiverphone" id="Receiverphone" placeholder="Mobile No">

							    </div>

							</div><!--end control-group-->

							<div class="control-group">

							    <label class="control-label" for="Receiveremail">E-Mail : </label>

							    <div class="controls">

							      <input type="email" name="Receiveremail" id="Receiveremail" placeholder="<EMAIL>">

							    </div>

							</div><!--end control-group-->

							<!--end control-group   <i class="icon-remove"></i> Invalid input!</span>-->

						<!--end control-group-->



						<!--<div class="control-group">

							<label class="control-label" > City : </label>

							<div class="controls">

							<select name="rcity" id="rcity" class="select2-search">

								<option value="">-- Please Select --</option>

							</select>

							</div>

						</div>--><!--end control-group-->



                            <div class="control-group success">

							    <label class="control-label" for="rzip">Zip Code: </label>

							    <div class="controls">

							      <input type="text" name="rzip" id= "rzip" placeholder="Zip Code">

							      <!--<span class="help-inline"><i class="icon-ok"></i> Avaliable input!</span>-->

							    </div>

							</div><!--end control-group-->

                             <!--end control-group-->

 <!--<div class="control-group success">

							    <label class="control-label" for="rzip">PAN No: </label>

							    <div class="controls">

							      <input type="text" name="rcpan" id="rcpan" placeholder="PAN No">

							      

							    </div>

							</div>-->

                       <div class="control-group">

							    <label class="control-label" for="rcstin">GST No: </label>

							    <div class="controls">

							      <input type="gstno" name="gstno" id="gstno" placeholder="GST No" >

							    </div>

							</div><!--end control-group--> 

                </div>  

             </div>

		   

		     <div class="row">	

			          

						<center>	<legend >&nbsp;&nbsp;&nbsp;&nbsp;Shipment info :  </legend></center>

							<div class="control-group">

					         <!--<div class="controls">

						 <label class="control-label"> <input type="radio" name="colorRadio" value="account" onclick="manually(this);" > Manually </label>

						 </div> </div>-->
						 <br>
						 
						 <div class="span12">
						     <div class="control-group">

								    <label class="control-label"> Shipment Type  : <span class="text-error">*</span></label>

								    <div class="controls">

								      <label class="control-label">
								          <input type="radio" name="shipmentType" value="regular" onclick="handleShipmentTypeChange()"> Regular
								      </label>
								      <label class="control-label">
								          <input type="radio" name="shipmentType" value="transfer" onclick="handleShipmentTypeChange()"> Transfer
								      </label>

								    </div>

							</div>
							<div class="clearfix"></div>
							<br>



							
<div id="transferDiv" style="display: none; width: 890px; min-height: 200px; border: 2px solid black; padding: 15px; background-color: #f9f9f9; border-radius: 6px;">
    <!-- Type Dropdown -->
    <div class="control-group" style="margin-bottom: 1element.style5px;">
        <label class="control-label" for="transferTypes">Type: <span class="text-error">*</span></label>
        <div class="controls">
            <select name="transferTypes" id="transferTypes" onchange="handleTransferTypeChange()" style="width: 200px;" class="select2-search">
                <option value="" selected="selected">-- Select Type --</option>
                <option value="__SEARCH__">🔍 Search...</option>
                <option value="inward">Inward</option>
                <option value="outward">Outward</option>
            </select>
        </div>
    </div>

    <!-- Inward Part Details Section -->
    <div id="transferInwardSection" style="display: none; margin-top: 20px;">
        <div style="margin-bottom: 20px;">
            <div class="transfer-row-flex" style="display: flex; gap: 10px; align-items: center; margin-bottom: 10px;">
                <label style="min-width: 80px;">Part Details:</label>
                <input type="text" name="transfer_part_no[]" placeholder="Part No" style="width: 200px;">
                <input type="text" name="transfer_quantity[]" placeholder="Quantity" style="width: 200px;">
                <input type="text" name="transfer_packages[]" placeholder="No. of Packages" style="width: 200px;">
            </div>
            <div id="transferRowsContainer"></div>
            <div style="margin-top: 10px;">
                <button type="button" id="addRowBtn" class="btn" onclick="addTransferRow()" style="background-color: white; color: black; padding: 8px 16px; border: none; border-radius: 4px;">Add Row</button>
            </div>
        </div>
    </div>

    <!-- Outward Part Details Section -->
<div id="transferOutwardSection" style="display: none; margin-top: 20px;">
    <div style="margin-bottom: 20px;">
        <div class="outward-row-flex" style="display: flex; gap: 10px; align-items: center; margin-bottom: 10px;">
            <label style="min-width: 80px;">Part Details:</label>

            <!-- 1. Part No Dropdown -->
            <select name="outward_part_no[]" style="width: 130px;" class="outward-part-select select2-search">
                <option value="">-- Select Part No --</option>
                <?php echo $partDropdown; ?>
            </select>

            <!-- 2. Existing Packages (readonly) -->
            <input type="text" name="existing_packages[]" placeholder="Existing Packages" style="width: 130px;" readonly>

            <!-- 3. New Packages -->
            <input type="text" name="outward_packages[]" placeholder="New Packages" style="width: 130px;">

            <!-- 4. Existing Quantity (readonly) -->
            <input type="text" name="existing_quantity[]" placeholder="Existing Quantity" style="width: 130px;" readonly>

            <!-- 5. New Quantity -->
            <input type="text" name="outward_quantity[]" placeholder="New Quantity" style="width: 130px;">
        </div>

        <div id="outwardRowsContainer"></div>

        <div style="margin-top: 10px;">
            <button type="button" id="addOutwardRowBtn" class="btn" onclick="addOutwardRow()" style="background-color: white; color: black; padding: 8px 16px; border: none; border-radius: 4px;">Add Row</button>
        </div>
    </div>
</div>



	                      <br> 
	                      <br>
						 </div>
						 

                      <div class="span6">        

							   

							 <div class="control-group" id="consman">

							    <label class="control-label" for="ConsignmentNo"> Docket No  : <span class="text-error">*</span></label>

							    <div class="controls">

							      <input type="text" name="ConsignmentNo" id="ConsignmentNo" placeholder="Docket No" onblur="consignmen()" value="<?php echo $cons; ?>" readonly>

							    

							    </div>

							</div>

						

							 

						

						

							 <div class="control-group ">

							    <label class="control-label" for="PartNo">Part No  : <span class="text-error">*</span></label>

							    <div class="controls">

							      <select name="PartNo" id="PartNo" onchange="fetchRegularPartDetails(this.value)" class="select2-search">
							          <option value="">-- Select Part No --</option>
							          <?php echo $partDropdown; ?>
							      </select>



							    </div>

							</div>

							

								 <div class="control-group ">

							    <label class="control-label" for="No. Of Packages"> No. Of Packages  : <span class="text-error">*</span></label>

							    <div class="controls">

							      <input type="text" name="noofpackages" id="noofpackages" placeholder="No. Of Packages" onblur="consignmen()">

							      

							    </div>

							</div>

							<!-- end control-group-->

							<!-- <div class="account box" style="display:none">

							    <label class="control-label">To Address: <span class="text-error">*</span></label> 

							        <div class="controls">

									    <select name="desti" id="desti" onchange="getrate()" class="select2-search">

									     <option value="">-- Please Select --</option>

										</select>

	                                </div>

								   <div class="controls"><br>

								       <input type="text" class="span1" name="des" id="des"placeholder="destination"2 value="From" readonly> 

									   <input type="text" class="span1" name="kg" id="kg" placeholder="Fuel" value="Fuel(%)" readonly>(%)

									   <input type="text" class="span1" name="rate" id="rate"  placeholder="rate" value="Rate" readonly>

									   <input type="text" class="span1" name="ftl" id="ftl"  placeholder="ftl" value="Unit" readonly>

									</div><br>

							</div> -->

							<!--<div class="control-group" id="toadd" >

							    <label class="control-label" for="desti">To Address:<span class="text-error">*</span> </label>

							    <div class="controls">

							      <input type="text" name="desti" placeholder="To Name Address" ><br>

								  <input type="hidden" name="rate1" id="rate1" placeholder="Rate " >

							    </div>

							</div>--><!--end control-group--> 

							

							<!--end control-group-->  

							<!--<div class="control-group" >

							    <label class="control-label" for="desti">Rate / Unit :<span class="text-error">*</span> </label>

							    <div class="controls">

							     <input type="text" class="span2" name="rate" id="rate"  placeholder="rate" >

								<input type="text" class="span1" name="unit1" id="unit1"  onchange ="hideqty(this.value)" placeholder="ftl"  >

								 </div>

								 

							</div>--><!--end control-group-->  



							

							<!--end control-group-->

                                                        

                                                     



                                                         <!--<div class="control-group">

							    <label class="control-label" for="round">Round :</label>

							    <div class="controls">

							        <select name="round" id="round">

									    <option selected="selected" value="">--Select Round--</option>

										<option value="sigl">Single</option>     

										<option value="round">Round</option>   

										

									</select>

			     			    </div>

							</div>--><!--end control-group-->    

							<div class="control-group">

							    <label class="control-label" for="Invoiceno">Invoice no : <span class="text-error">*</span>	</label>

							    <div class="controls" >

							      <input type="text" name="Invoiceno1" id="Invoiceno1" placeholder="invoice No">
								  
							
								</div>

							</div>

							 <input type="hidden" name="cnt" id="cnt" value="1" style="width:50px">



								<div class="control-group">

								    <div class="controls" id="textboxDiv">

								</div></div>

							    

							  

						

							<div class="control-group">

							    <label class="control-label" for="invalue">Invoice Value : </label>

							    <div class="controls">

							      <input type="text" name="invalue" id="invalue" placeholder="Invoice Value" value="0" onkeyup="getValues2()">

							    </div>

							</div><!--end control-group-->

							

							 <div class="control-group">

							    <label class="control-label" for="Mode">Booking Type :<span class="text-error">*</span></label>

							    <div class="controls">

							        <select name="Mode" id="Mode" onclick="getValues(this.value);" class="select2-search">

									    <option selected="selected" value="">--Select Booking Type--</option>

									    <option value="__SEARCH__">🔍 Search...</option>

										<option value="Air">Air Express</option>

										<option value="Road">Surface Express</option>

										<option value="Train">Train</option>

										<option value="International">International</option>

										<option value="Sea">Sea</option>

									</select>

			     			    </div>

							</div><!--end control-group-->

							 <div class="control-group">

							    <label class="control-label" for="delivery">Delivery Type :<span class="text-error">*</span></label>

							    <div class="controls">

							        <select name="delivery" id="delivery" class="select2-search">



										<option value="door">Door Delivery </option>



									</select>

			     			    </div>

							</div><!--end control-group-->

												

							

						  

							<div class="control-group">

							    <label class="control-label" for="Shiptype">Type of Packing : <span class="text-error">*</span></label>

							    <div class="controls">

							        <select name="Shiptype" id="Shiptype" class="select2-search">

									    <option selected="selected" value="">--Please Select--</option>

										<option value="CTN-BOX">CTN-BOX</option>

										<option value="WB-BOX">WB-BOX</option>

										<option value="Loose ">Loose </option>

										<option value="Gany">Gany Bag</option>



									</select>

			     			    </div>

							</div><!--end control-group-->

						

							<div class="control-group">

							    <label class="control-label" for="descript">Shipment Description:</label>

							    <div class="controls">

                                   <input type="text" name="descript" id="descript" placeholder="" >

								  

							        <span class="help-inline">

							       

							    </div>

							</div><!--end control-group-->

						<!--end control-group-->

							<div class="control-group " id="Weight1">

							    <label class="control-label" for="Weight"> Actual Weight : </label>

							    <div class="controls">

                                  <input type="text" name="Weight" id="Weight" placeholder="Actual Weight in KG" onkeyup="getValues1()"> 

							      <span class="help-inline">

							    </div>

							</div><!--end control-group-->

                           <!--end control-group--> 

                            <div class="control-group " id="cweight1">

							    <label class="control-label" for="Chargeable Weight"> Chargeable Weight : </label>

							    <div class="controls">

							      <input type="text" name="cweight" id="cweight" placeholder="Chargeable Weight in KG" onkeyup="getValues1()">  

							     

							    </div>

							</div>



                                 <div class="control-group" id="volwem1">  

                                     <label class="control-label" for="Comments">Volumetric Weight :</label>								 

					                <div class="controls-row"  >

										<input type="text" class="span1" name="wei" id="wei"  placeholder="width" onkeyup="getValues()"/>

										<input type="text" class="span1" name="len" id="len" placeholder="lenght" onkeyup="getValues()"/>

										<input type="text" class="span1" name="hei" id="hei"  placeholder="height" onkeyup="getValues()"/>

										<input type="text" class="span1" name="tot" id="tot"  placeholder="total" onkeyup="getValues1()"/>

									</div>

							     </div>

							     

							     <div class="control-group">

							    <label class="control-label" for="Vehicle">Vehicle No.:<span class="text-error">*</span> </label>

							    <div class="controls">

							      <input type="text" name="vehicleNo" id="vehicleNo" placeholder="Vehicle No.">

							    </div>

							</div>

							     

	 	<div class="control-group">

							    <label class="control-label" for="clerkname">Staff Name: </label>

							    <div class="controls">

							      <input type="text" name="clerkname" placeholder="Staff Name " value="<?php echo $clrkname; ?>" >

							    </div>

							</div><!--end control-group--> 

							

                             <div class="control-group">

							    <label class="control-label" for="clerkno">Staff Contact No: </label>

							    <div class="controls">

							      <input type="text" name="clerkno" placeholder="Staff Contact No" >

							    </div></div></br></br></br></br></br></br></br></br></br></br></br></br></br>

							<!--end control-group--> 

							

                            

				    <div class="control-group">

							    <label class="control-label" for="Packupdate">Booking Date  :</label>

							    <div class="controlsa">

							      

										 <div class="controls input-append date form_date" data-date="" data-date-format="dd MM yyyy hh:mm:ss" data-link-field="dtp_input2" data-link-format="yyyy-mm-dd">

										

											<input type="text" name="Packupdate" id="Packupdate"  value=""  readonly required>

											<span class="add-on"><i class="icon-remove"></i></span>

											<span class="add-on"><i class="icon-th"></i></span>

										</div>

										     <input type="hidden" id="dtp_input2" value="" />

		

										   

							    </div>

							    </div>

							    

							    <div class="control-group">

							    <label class="control-label" for="Packupdate">Assured Dly Date  :</label>

							    <div class="controlsa">

							      

										 <div class="controls input-append date form_date" data-date="" data-date-format="dd MM yyyy hh:mm:ss" data-link-field="dtp_input2" data-link-format="yyyy-mm-dd">

											<input type="text" name="Packupdate1" id="Packupdate1"  value=""  readonly required>

											<span class="add-on"><i class="icon-remove"></i></span>

											<span class="add-on"><i class="icon-th"></i></span>

										</div>

										     <input type="hidden" id="dtp_input2" value="" />

							    </div>

							</div><!--end control-group-->

			</div>	

			

			

			

			<div class="span4">	

			 <div class="control-group" id="asdf">

							    <label class="control-label" for="E-Way-Bill">E-Way Bill:<span class="text-error">*</span> </label>

							    <div class="controls">

							    <input type="text" name="ewaybill" id="ewaybill" placeholder="E-Way Bill" />

								 </div>  

								  <input type="hidden" name="rowcount" id="rowcount" value="1" style="width:50px">

								  

							</div></div>


							
<div class="span4">  
  <div class="control-group">
    <label class="control-label" for="startdate">Start Date:<span class="text-error">*</span></label>
    <div class="controls input-append date form_date" data-date="" data-date-format="dd MM yyyy hh:mm:ss" data-link-field="start_input" data-link-format="yyyy-mm-dd">
      <input type="text" name="startdate" id="startdate" value="" readonly required>
      <span class="add-on"><i class="icon-remove"></i></span>
      <span class="add-on"><i class="icon-th"></i></span>
    </div>
  </div>
</div>

<div class="span4">  
  <div class="control-group">
    <label class="control-label" for="enddate">End Date:<span class="text-error">*</span></label>
    <div class="controls input-append date form_date" data-date="" data-date-format="dd MM yyyy hh:mm:ss" data-link-field="end_input" data-link-format="yyyy-mm-dd">
      <input type="text" name="enddate" id="enddate" value="" readonly required>
      <span class="add-on"><i class="icon-remove"></i></span>
      <span class="add-on"><i class="icon-th"></i></span>
    </div>
  </div>
</div>


			

				<div class="span4">		

							<div class="control-group" id="Qnty1">

							    <label class="control-label" for="Qnty">Qnty :</label>

							    <div class="controls">

							      <input type="text" name="Qnty" id="Qnty" placeholder="Qnty" onkeyup="getValues1()" >

							    </div>

							</div><!--end control-group-->

                             



							

							<div class="control-group" id="Qnty1">

							    <label class="control-label" for="Rate">Rate:</label> 

							    <div class="controls">

							      <input type="text" name="rate" id="rate" placeholder="Rate" onkeyup="getValues1()" >

							    </div>

							</div>

							<!--end control-group-->

							

							

							

						

                           

								 <div class="control-group">

							    <label class="control-label" for="Totalfreight">Total Freight :</label>

							    <div class="controls">

							     

							  <input type="text" name="Totalfreight"  id="Totalfreight" placeholder="Total freight" value="0" onkeyup="getValues2()" >

							  

							    </div>

							    </div>

							    

							    

							

								

                                 <div class="control-group" id="volwem1">  

                                     <label class="control-label" for="Comments">DPH Charges :</label>								 

					                <div class="controls-row"  >

										<input type="text" class="span1" name="DPH" id="DPH"  placeholder="DPH" onkeyup="getValues2()"/>

										<input type="text" class="span1" name="dphpercent" id="dphpercent" Value="0" onkeyup="getValues2()"/>

									

							     

							    </div>

							</div><!--end control-group-->

							

								<div class="control-group">

							    <label class="control-label" for="ROV">ROV Charges: </label>

							    <div class="controls-row"  >

							      <input type="text" class="span1" name="rov"  id="rov" placeholder="ROV"  onkeyup="getValues2()">

							      	<input type="text" class="span1" name="rovpercent" id="rovpercent" Value="0" onkeyup="getValues2()"/>

									

							    </div>

							</div><!--end control-group-->

							

							

							<div class="control-group">

							    <label class="control-label" for="docharg">Docket Charges :</label>

							    <div class="controls">

							      <input name="docharg" value="0" id="docharg" onkeyup="getValues2()"  >

							    </div>

							</div>

							

								<div class="control-group">

							    <label class="control-label" for="docharg">Delivery Charges :</label>

							    <div class="controls">

							      <input name="Deliverycharg" value="0" id="Deliverycharg" onkeyup="getValues2()"  >

							    </div>

							</div>

							

							 <div class="control-group">

							    <label class="control-label" for="oda">ODA/ESS:</label>

							    <div class="controls">

							      <input type="text" name="oda" id="oda" onkeyup="getValues2()" value="0">

							    </div></div>

							    

							    	<div class="control-group">

							    <label class="control-label" for="misc">Handling Charges: </label>

							    <div class="controls">

							      <input type="text" name="handlingcharge" id="handlingcharge" placeholder=" "  value="0" onkeyup="getValues2()" >

							    </div>

							</div><!--end control-group--> 

							

							  <div class="control-group">

							    <label class="control-label" for="codod">COD / FOD/ToPay :</label>

							    <div class="controls">

							      <input type="text" name="codod" id="codod" onkeyup="getValues2()" value="0">

							    </div>

							</div><!--end control-group-->



                           <div class="control-group">

							    <label class="control-label" for="FOV"> Fov Charges:</label>

							    <div class="controls">

							      <input type="text" name="fov" id="fov" onkeyup="getValues2()" value="0">

							    </div>

							</div>

							

							

							 <div class="control-group">

							    <label class="control-label" for="codod">COD / FOD/ToPay :</label>

							    <div class="controls">

							      <input type="text" name="codod" id="codod" onkeyup="getValues2()" value="0">

							    </div>

							</div>

							

							 <div class="control-group">

							    <label class="control-label" for="other"> Other Charges:</label>

							    <div class="controls">

							      <input type="text" name="othercharge" id="othercharge" onkeyup="getValues2()" value="0">

							    </div>

							</div>



							<div class="control-group">

							    <label class="control-label" for="amt">Sub Total </label>

							    <div class="controls">

							      <input type="text" name="amt" id="amt"  readonly value="0">

							    </div></div>	<!--end control-group-->

						

							    	<div class="control-group">

							    <label class="control-label" for="CGST">CGST  :</label>

							    <div class="controls">

							      <input name="CGST"  id="CGST" onkeyup="getValues2()" readonly value="0">

							        

							    </div><!--end control-group-->

							</div><!--end control-group-->

								<div class="control-group">

							    <label class="control-label" for="SGST">SGST  :</label>

							    <div class="controls">

							      <input name="SGST"  id="SGST" onkeyup="getValues2()" readonly value="0">

							        

							    </div>

							</div><!--end control-group-->

								<div class="control-group">

							    <label class="control-label" for="amt">Grand Total </label>

							    <div class="controls">

							      <input type="text" name="grandamt" id="grandamt"  readonly value="0">

							    </div></div>

					         <input type="hidden" name="status" value="Booking">

				</div>	</div><!--end row-->		 
<div class="control-group">
<label class="control-label" for="amt">User Id </label>
<div class="controls">
<input type="number" name="cid" id="cid" value="<?php echo $_SESSION['desgn']; ?>" readonly>
</div></div>


							<div class="control-group">

							    <div class="controls">

							     <input   type="hidden" name="mydoc" id="mydoc"   onblur="consignmen()" value="<?php echo $cons; ?>" >
								     <input type="hidden" name="expdate" id="expdate" value="">

									<input name="submit" class="btn btn-primary" type="submit" value="Save & Print" onClick="return validation()">

									<input name="save" class="btn btn-primary" type="submit" value="Save" onClick="return validation()">

									<button type="reset" class="btn ">Clear</button>

							    </div>

							</div><!--end control-group-->

		

		</form><!--end form-->	

</div><!--end conatiner-->

<script>

function validation(){
	console.log("Validation function called for shipment type");

	// Get shipment type from radio buttons
	var shipmentType = "";
	var shipmentRadios = document.getElementsByName('shipmentType');
	for(var i = 0; i < shipmentRadios.length; i++) {
		if(shipmentRadios[i].checked) {
			shipmentType = shipmentRadios[i].value;
			break;
		}
	}
	console.log("Current shipment type:", shipmentType);

	// Check if shipment type is selected
	if(shipmentType === "") {
		alert("Please select a shipment type (Regular or Transfer)");
		return false;
	}

	// If transfer is selected, check if transfer type is selected
	if(shipmentType === "transfer") {
		var transferType = document.getElementById('transferTypes').value;
		if(transferType === "") {
			alert("Please select transfer type (Inward or Outward)");
			return false;
		}
	}

	// Simple validation - just check if form exists
	if(!document.forms["bookshipment"]) {
		alert("Form not found");
		return false;
	}

	console.log("Form validation passed - allowing submission");
	return true; // Allow normal form submission
}

</script>









<link href="./bootstrap/css/bootstrap-datetimepicker.min.css" rel="stylesheet" media="screen">

	<script type="text/javascript" src="./jquery/jquery-1.8.3.min.js" charset="UTF-8"></script>

<script type="text/javascript" src="./bootstrap/js/bootstrap.min.js"></script>

<script type="text/javascript" src="./jquery/bootstrap-datetimepicker.js" charset="UTF-8"></script>

<!--<script type="text/javascript" src="./jquery/locales/bootstrap-datetimepicker.pl.js" charset="UTF-8"></script>-->

<script type="text/javascript">

	$('.form_date').datetimepicker({

        language:  'pl',

        weekStart: 1,

        todayBtn:  1,

		autoclose: 1,

		todayHighlight: 1,

		startView: 2,

		minView: 2,

		forceParse: 0

    });

	$('.form_time').datetimepicker({

        language:  'pl',

        weekStart: 1,

        todayBtn:  1,

		autoclose: 1,

		todayHighlight: 1,

		startView: 1,

		minView: 0,

		maxView: 1,

		forceParse: 0

    });

</script>

<link rel="stylesheet" type="text/css" media="all" href="jsDatePick_ltr.min.css" />

<script type="text/javascript" src="jsDatePick.min.1.3.js"></script>

<script type="text/javascript">

	window.onload = function(){

	

		new JsDatePick({

			useMode:2,

			target:"db",

			dateFormat:"%d-%m-%Y"



		});

		

		

	};

</script>

<style>

table {

		overflow:hidden;

		border:1px solid #d3d3d3;

		background:#fefefe;

		width:100%;

		margin:5% auto 0;

		-moz-border-radius:5px; /* FF1+ */

		-webkit-border-radius:5px; /* Saf3-4 */

		border-radius:5px;

		-moz-box-shadow: 0 0 4px rgba(0, 0, 0, 0.2);

		-webkit-box-shadow: 0 0 4px rgba(0, 0, 0, 0.2);

	}

	

	th, td {padding:18px 28px 18px; text-align:center; }

	

	th {padding-top:22px; text-shadow: 1px 1px 1px #fff; background:#e8eaeb;}

	

	td {border-top:1px solid #e0e0e0; border-right:1px solid #e0e0e0;}

	

	tr.odd-row td {background:#f6f6f6;}

	

	td.first, th.first {text-align:left}

	

	td.last {border-right:none;}

	

	/*

	Background gradients are completely unnecessary but a neat effect.

	*/

	

	td {

		background: -moz-linear-gradient(100% 25% 90deg, #fefefe, #f9f9f9);

		background: -webkit-gradient(linear, 0% 0%, 0% 25%, from(#f9f9f9), to(#fefefe));

	}

	

	tr.odd-row td {

		background: -moz-linear-gradient(100% 25% 90deg, #f6f6f6, #f1f1f1);

		background: -webkit-gradient(linear, 0% 0%, 0% 25%, from(#f1f1f1), to(#f6f6f6));

	}

	

	th {

		background: -moz-linear-gradient(100% 20% 90deg, #e8eaeb, #ededed);

		background: -webkit-gradient(linear, 0% 0%, 0% 20%, from(#ededed), to(#e8eaeb));

	}

	

	/*

	I know this is annoying, but we need additional styling so webkit will recognize rounded corners on background elements.

	Nice write up of this issue: http://www.onenaught.com/posts/266/css-inner-elements-breaking-border-radius

	

	And, since we've applied the background colors to td/th element because of IE, Gecko browsers also need it.

	*/

	

	tr:first-child th.first {

		-moz-border-radius-topleft:5px;

		-webkit-border-top-left-radius:5px; /* Saf3-4 */

	}

	

	tr:first-child th.last {

		-moz-border-radius-topright:5px;

		-webkit-border-top-right-radius:5px; /* Saf3-4 */

	}

	

	tr:last-child td.first {

		-moz-border-radius-bottomleft:5px;

		-webkit-border-bottom-left-radius:5px; /* Saf3-4 */

	}

	

	tr:last-child td.last {

		-moz-border-radius-bottomright:5px;

		-webkit-border-bottom-right-radius:5px; /* Saf3-4 */

	}



</style>



<script language="JavaScript" type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jquery/1.11.1/jquery.min.js"></script>



<script>

function getValues(val){

	var numVal1 = Number(document.getElementById("hei").value);

	var numVal2 = Number(document.getElementById("wei").value);

	var numVal3 = Number(document.getElementById("len").value);

	//var numVal5 = Number(document.getElementById("freight").value);

	if(val=="Air"){

	var totalValue = (numVal1 * numVal2 * numVal3 )/5000;

	document.getElementById("tot").value = totalValue;

}else{

    	var totalValue = (numVal1 * numVal2 * numVal3 )/1728*10;

	document.getElementById("tot").value = totalValue;

}

}



function hideqty(id){

	var rat1=document.getElementById("rate").value;

		//alert(rat1);

	if(id=="ftl"){

	//alert("ftl");

		$("#Weight1").hide();

		$("#cweight1").hide();

		$("#volwem1").hide();

		$("#Qnty1").hide();

		

		$("#totfre").val(rat1);

	}

}

</script>

	

<script type="text/javascript" src="https://code.jquery.com/jquery.min.js"></script>

<script type="text/javascript">

/*$(document).ready(function(){

    $('input[type="radio"]').click(function(){

        if($(this).attr("value")=="account"){

            $(".box").not(".account").hide();

            $(".account").show();

        }

        if($(this).attr("value")=="cash"){

            $(".box").not(".cash").hide();

            $(".cash").show();

        }

      });

});*/

</script>







<script type="text/javascript">

function consignmen()

{

	var a=document.getElementById("ConsignmentNo").value;

//alert(a);

obja=new XMLHttpRequest();

obja.open("GET","constt.php?a="+a,true);

obja.send();

obja.onreadystatechange=func

}

function func()

{

	//alert("hi");

if(obja.readyState==4)

{

	valar=obja.responseText;

	if(valar!="")

	{

		alert(valar)

	}

}

}





// <!-- <![CDATA[



// Project: Dynamic Date Selector (DtTvB) - 2006-03-16

// Script featured on JavaScript Kit- http://www.javascriptkit.com

// Code begin...

// Set the initial date.

var ds_i_date = new Date();

ds_c_month = ds_i_date.getMonth() + 1;

ds_c_year = ds_i_date.getFullYear();



// Get Element By Id

function ds_getel(id) {

	return document.getElementById(id);

}



// Get the left and the top of the element.

function ds_getleft(el) {

	var tmp = el.offsetLeft;

	el = el.offsetParent

	while(el) {

		tmp += el.offsetLeft;

		el = el.offsetParent;

	}

	return tmp;

}

function ds_gettop(el) {

	var tmp = el.offsetTop;

	el = el.offsetParent

	while(el) {

		tmp += el.offsetTop;

		el = el.offsetParent;

	}

	return tmp;

}



// Output Element

var ds_oe = ds_getel('ds_calclass');

// Container

var ds_ce = ds_getel('ds_conclass');



// Output Buffering

var ds_ob = ''; 

function ds_ob_clean() {

	ds_ob = '';

}

function ds_ob_flush() {

	ds_oe.innerHTML = ds_ob;

	ds_ob_clean();

}

function ds_echo(t) {

	ds_ob += t;

}



var ds_element; // Text Element...



var ds_monthnames = [

'January', 'February', 'March', 'April', 'May', 'June',

'July', 'August', 'September', 'October', 'November', 'December'

]; // You can translate it for your language.



var ds_daynames = [

'Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'

]; // You can translate it for your language.



// Calendar template

function ds_template_main_above(t) {

	return '<table cellpadding="3" cellspacing="1" class="ds_tbl">'

	     + '<tr>'

		 + '<td class="ds_head" style="cursor: pointer" onclick="ds_py();">&lt;&lt;</td>'

		 + '<td class="ds_head" style="cursor: pointer" onclick="ds_pm();">&lt;</td>'

		 + '<td class="ds_head" style="cursor: pointer" onclick="ds_hi();" colspan="3">[Close]</td>'

		 + '<td class="ds_head" style="cursor: pointer" onclick="ds_nm();">&gt;</td>'

		 + '<td class="ds_head" style="cursor: pointer" onclick="ds_ny();">&gt;&gt;</td>'

		 + '</tr>'

	     + '<tr>'

		 + '<td colspan="7" class="ds_head">' + t + '</td>'

		 + '</tr>'

		 + '<tr>';

}



function ds_template_day_row(t) {

	return '<td class="ds_subhead">' + t + '</td>';

	// Define width in CSS, XHTML 1.0 Strict doesn't have width property for it.

}



function ds_template_new_week() {

	return '</tr><tr>';

}



function ds_template_blank_cell(colspan) {

	return '<td colspan="' + colspan + '"></td>'

}



function ds_template_day(d, m, y) {

	return '<td class="ds_cell" onclick="ds_onclick(' + d + ',' + m + ',' + y + ')">' + d + '</td>';

	// Define width the day row.

}



function ds_template_main_below() {

	return '</tr>'

	     + '</table>';

}



// This one draws calendar...

function ds_draw_calendar(m, y) {

	// First clean the output buffer.

	ds_ob_clean();

	// Here we go, do the header

	ds_echo (ds_template_main_above(ds_monthnames[m - 1] + ' ' + y));

	for (i = 0; i < 7; i ++) {

		ds_echo (ds_template_day_row(ds_daynames[i]));

	}

	// Make a date object.

	var ds_dc_date = new Date();

	ds_dc_date.setMonth(m - 1);

	ds_dc_date.setFullYear(y);

	ds_dc_date.setDate(1);

	if (m == 1 || m == 3 || m == 5 || m == 7 || m == 8 || m == 10 || m == 12) {

		days = 31;

	} else if (m == 4 || m == 6 || m == 9 || m == 11) {

		days = 30;

	} else {

		days = (y % 4 == 0) ? 29 : 28;

	}

	var first_day = ds_dc_date.getDay();

	var first_loop = 1;

	// Start the first week

	ds_echo (ds_template_new_week());

	// If sunday is not the first day of the month, make a blank cell...

	if (first_day != 0) {

		ds_echo (ds_template_blank_cell(first_day));

	}

	var j = first_day;

	for (i = 0; i < days; i ++) {

		// Today is sunday, make a new week.

		// If this sunday is the first day of the month,

		// we've made a new row for you already.

		if (j == 0 && !first_loop) {

			// New week!!

			ds_echo (ds_template_new_week());

		}

		// Make a row of that day!

		ds_echo (ds_template_day(i + 1, m, y));

		// This is not first loop anymore...

		first_loop = 0;

		// What is the next day?

		j ++;

		j %= 7;



	}

	// Do the footer

	ds_echo (ds_template_main_below());

	// And let's display..

	ds_ob_flush();

	// Scroll it into view.

	ds_ce.scrollIntoView();

}



// A function to show the calendar.

// When user click on the date, it will set the content of t.

function ds_sh(t) {

	// Set the element to set...

	ds_element = t;

	// Make a new date, and set the current month and year.

	var ds_sh_date = new Date();

	ds_c_month = ds_sh_date.getMonth() + 1;

	ds_c_year = ds_sh_date.getFullYear();

	// Draw the calendar

	ds_draw_calendar(ds_c_month, ds_c_year);

	// To change the position properly, we must show it first.

	ds_ce.style.display = '';

	// Move the calendar container!

	the_left = ds_getleft(t);

	the_top = ds_gettop(t) + t.offsetHeight;

	ds_ce.style.left = the_left + 'px';

	ds_ce.style.top = the_top + 'px';

	// Scroll it into view.

	ds_ce.scrollIntoView();

}



// Hide the calendar.

function ds_hi() {

	ds_ce.style.display = 'none';

}



// Moves to the next month...

function ds_nm() {

	// Increase the current month.

	ds_c_month ++;

	// We have passed December, let's go to the next year.

	// Increase the current year, and set the current month to January.

	if (ds_c_month > 12) {

		ds_c_month = 1; 

		ds_c_year++;

	}

	// Redraw the calendar.

	ds_draw_calendar(ds_c_month, ds_c_year);

}



// Moves to the previous month...

function ds_pm() {

	ds_c_month = ds_c_month - 1; // Can't use dash-dash here, it will make the page invalid.

	// We have passed January, let's go back to the previous year.

	// Decrease the current year, and set the current month to December.

	if (ds_c_month < 1) {

		ds_c_month = 12; 

		ds_c_year = ds_c_year - 1; // Can't use dash-dash here, it will make the page invalid.

	}

	// Redraw the calendar.

	ds_draw_calendar(ds_c_month, ds_c_year);

}



// Moves to the next year...

function ds_ny() {

	// Increase the current year.

	ds_c_year++;

	// Redraw the calendar.

	ds_draw_calendar(ds_c_month, ds_c_year);

}



// Moves to the previous year...

function ds_py() {

	// Decrease the current year.

	ds_c_year = ds_c_year - 1; // Can't use dash-dash here, it will make the page invalid.

	// Redraw the calendar.

	ds_draw_calendar(ds_c_month, ds_c_year);

}



// Format the date to output.

function ds_format_date(d, m, y) {

	// 2 digits month.

	m2 = '00' + m;

	m2 = m2.substr(m2.length - 2);

	// 2 digits day.

	d2 = '00' + d;

	d2 = d2.substr(d2.length - 2);

	// YYYY-MM-DD

	return d2 + '/' + m2 + '/'+ y;

}



// When the user clicks the day.

function ds_onclick(d, m, y) {

	// Hide the calendar.

	ds_hi();

	// Set the value of it, if we can.

	if (typeof(ds_element.value) != 'undefined') {

		ds_element.value = ds_format_date(d, m, y);

	// Maybe we want to set the HTML in it.

	} else if (typeof(ds_element.innerHTML) != 'undefined') {

		ds_element.innerHTML = ds_format_date(d, m, y);

	// I don't know how should we display it, just alert it to user.

	} else {

		alert (ds_format_date(d, m, y));

	}

}



function getSelected(opt)

 {

 

 	var opt=document.frmExport.opt;

            for (var intLoop = 0; intLoop < opt.length; intLoop++)

			 {

			  if (!(opt.options[intLoop].selected))

			   {

			   		alert("Select any one field!");

					return false;

               }

		    }

			return true;           

  }



// And here is the end.



// ]]> 

</script>  



<script>



function to() { 

 // alert("hhi");

   $('#desti').find('option').remove().end().append('<option value="other">----Select Destinantion----</option>').val('');

    $.ajax({                                      

      url: 'ajax_getDestination.php?type='+$('#sname').val(),                  //the script to call to get data          

      data: "",                        //you can insert url argumnets here to pass to api.php

                                       //for example "id=5&parent=6"

      dataType: 'json',                //data format      

      success: function(data)          //on recieve of reply

      {

      $.each(data, function(index, data) {

        $('#desti').append( $('<option></option>').val(data.id).html(data.name) );

       });

       }

       });

       

    }

	

</script>



<script>

function addre()

{

	document.getElementById("toadd").style.display="none";

}



</script>

<script>

function addres()

{  //alert("addres");

document.getElementById("toadd").style.display="block";

}



</script>



<script  type="text/javascript">

function getrate()

{ 

var adest=document.getElementById("desti").value;

//alert(adest); 

obj=new XMLHttpRequest();

obj.open("GET","ajax_getrate1.php?type="+adest,true);

obj.send();

obj.onreadystatechange=funcrate

}

function funcrate()

{

   if(obj.readyState==4)

     {

	getdata=obj.responseText;

	//alert(getdata);

	var destdata = getdata.split("*");

		//alert(destdata[0]);

		document.getElementById("des").value=destdata[0];

		document.getElementById("rate").value=destdata[1];

		document.getElementById("kg").value=destdata[2];

		document.getElementById("ftl").value=destdata[3];

		var uni=destdata[3];

		//alert(uni);

		unit(uni);

		}	

}

function unit(ui)

{

if(ui=="ftl")

{

	var rat1 = Number(document.getElementById("rate").value);

	//alert(rat1);

	document.getElementById("totfre").value = rat1;

	



}

else{

	getValues1();

}



}

</script>

	

<script>

function getValues1(){



    var rate = Number(document.getElementById("rate").value);

    var actweight = Number(document.getElementById("Weight").value);

    var chweight = Number(document.getElementById("cweight").value);

    var volumweight = Number(document.getElementById("tot").value);

    var qtyq = Number(document.getElementById("Qnty").value);

    

    var totwe= actweight + chweight + volumweight ;

   

    var totfre1=  rate * chweight;

     

     document.getElementById("Totalfreight").value = totfre1;



	

}





</script>



<script>

function getValues2(){

	var totfri = Number(document.getElementById("Totalfreight").value);

	

	var dph = Number(document.getElementById("DPH").value);

	var rov = Number(document.getElementById("rov").value);

	var fov = Number(document.getElementById("fov").value);

	var othercharge = Number(document.getElementById("othercharge").value);

	var docha = Number(document.getElementById("docharg").value);

	var oda1 = Number(document.getElementById("oda").value);

	var chwght = Number(document.getElementById("cweight").value);

    var handling = Number(document.getElementById("handlingcharge").value);

	var cod = Number(document.getElementById("codod").value);

	 var dphpercent = Number(document.getElementById("dphpercent").value);

	  var invalue = Number(document.getElementById("invalue").value);

	var rovpercent = Number(document.getElementById("rovpercent").value);

	 var Deliverycharg = Number(document.getElementById("Deliverycharg").value);

	

var dphcharges = totfri*dphpercent/100;

var rovcharges = invalue*rovpercent/100;

document.getElementById("DPH").value=dphcharges;

document.getElementById("rov").value=rovcharges;

	var totall= totfri + docha + cod + oda1 + rov + dph + handling + Deliverycharg + fov+ othercharge;

	var ctot=(totall*9)/100;

	var stot=(totall*9)/100;

	

    var gtotal=ctot+stot+totall;

    document.getElementById("amt").value = totall;

	document.getElementById("CGST").value = ctot;

	document.getElementById("SGST").value = stot;

	document.getElementById("grandamt").value = gtotal;

}

</script>



<script>

function insur(insu){

	

	if(insu=="ys")

	{  

	document.getElementById("insurance").style.display="block";

		

	}

	else{

	document.getElementById("insurance").style.display="none";

	}

}

</script>  



<script>

function insad(insu1){

	//alert(insu1);

	if(insu1=="other")

	{  

	document.getElementById("desti1").style.display="block";

		

	}

	else{

	document.getElementById("desti1").style.display="none";

	}

}



 function asd(ase){

	//alert(ase);

	if(ase!=="")

	{

		$("#asdf").hide();

	}

	

</script>  



      

<script>



$(document).ready(function ()

  {

   

   $("#sname").change(function () { 

    $('#Shipperphone').val("0");

      $('#Shipperemail').val("");   

     $('#Shipperaddress').val("");

       $('#custin').val("0");   

       $('#custgst').val("0"); 

        $('#custpan').val("0");   

    $.ajax({ 

    

                                           

      url: 'ajax_getShipdetails.php?sname='+$('#sname').val(),                  //the script to call to get data          

      data: "",                        //you can insert url argumnets here to pass to api.php

                                       //for example "id=5&parent=6"

      dataType: 'json',                //data format      

      success: function(data)          //on recieve of reply

      {

       

      $.each(data, function(index, data) {

         

     $('#Shipperphone').val(data.mob);

      $('#Shipperemail').val(data.email);   

   $('#Shipperaddress').val(data.Addr);

       $('#custin').val(data.custtin);   

       $('#custstax').val(data.custtx); 

        $('#custpan').val(data.custpans); 			 	

      

       });

  }

   });

  

    });

  }); 

  

</script>





<script src="//ajax.googleapis.com/ajax/libs/jquery/1.10.2/jquery.min.js"></script>

 <script>           

            

$(document).ready(function() { 

$('#rccode').on("change",function()

  {

   var rccode=$("#rccode").val();

   //alert(rccode);

   

 $.ajax({                 

      url:'ajax_getCity.php',   //the script to call to get data          

      method:'POST',         //send it through post method

      data: { 

      rcid:rccode,

},                         //for example "id=5&parent=6"

      dataType:'json',                //data format      

      success: function(data)          //on recieve of reply

      {

           $("#Receiveraddress").empty();

          

      $.each(data, function(index, data) {

        $('#Receiveraddress').append( $('<option></option>').val(data.id).html(data.name) );

       // rccode=0;

       });

       }

       });

}); 

        }); 

    </script>





<script>





    

   function paymentMode(t){



	var a = ["TBB"];

	var b = ["Paid","ToPay","FOC","ToPay&COD"];

	var c = ["TBB","Paid","ToPay","FOC","ToPay&COD"];

	s = document.getElementById('Bookingmode');

	var sl = s.options.length;

	for(var i = sl-1; i >= 0 ; i--) { s.options[i] = null; }

	if(t.value != 0){

		var z;

		switch (t.value) {

			case 'account' : z = a; break;

			case 'cash' : z = b; break;

			case 'walkin' : z = c; break;

			default : alert('Invalid entry'); break;

		}

		var l = z.length;

		for(i = 0; i < l; i++ ) { s.options[i] = new Option(z[i],z[i],false,false); }

	}

}





	

</script>







<script>

    $(document).ready(function() {

var iCnt = 1;

$('#add').click(function() {

 alert("hi");

if (iCnt <= 10) {

iCnt = iCnt + 1;

$('#cnt').val(iCnt);

// ADD TEXTBOX.

$("#textboxDiv").append('<div><br><input type="text" name="Invoiceno'+iCnt+'"  id="Invoiceno'+iCnt+'" placeholder="Invoice No"/><br></div>'); 

// ADD BOTH THE DIV ELEMENTS TO THE "main" CONTAINER.

 $('#main').after(container, divSubmit);

}

});

// REMOVE ONE ELEMENT PER CLICK.

$("#del").click(function() {  

    alert("del");

 //$("#textboxDiv").children().last().remove();  

});

 });     

       

$(document).ready(function() {

var rowCnt = 1;

$('#addeway').click(function() {

alert("hi");

if (rowCnt <= 10) {

rowCnt = rowCnt + 1;

$('#rowcount').val(rowCnt);

// ADD TEXTBOX.

//$("#Div").append('<div><br><input type="text" name="ewaybill'+rowCnt+'"  id="ewaybill'+rowCnt+'" placeholder="E Way BillNo"/><br></div>'); 

 ADD BOTH THE DIV ELEMENTS TO THE "main" CONTAINER.

 $('#main').after(container, divSubmit);

}

});

// REMOVE ONE ELEMENT PER CLICK.

$("#del").click(function() {  

   alert("del");

 //$("#textboxDiv").children().last().remove();  

});

 });     

       

    

</script>



<script src="select2.min.js"></script>

<script>
$(document).ready(function() {
    // Initialize Select2 with search functionality
    $('.select2-search').select2({
        placeholder: "-- Please Select --",
        allowClear: true,
        width: '100%',
        dropdownAutoWidth: true,
        theme: 'bootstrap'
    });

    // Fallback: Custom search functionality for regular dropdowns
    function addSearchToSelect(selectElement) {
        var $select = $(selectElement);
        var $wrapper = $('<div class="dropdown-search-wrapper"></div>');
        var $searchInput = $('<input type="text" class="form-control dropdown-search-input" placeholder="Search options..." style="margin-bottom: 5px;">');

        $select.before($wrapper);
        $wrapper.append($searchInput);
        $wrapper.append($select);

        var originalOptions = $select.find('option').clone();

        $searchInput.on('keyup', function() {
            var searchTerm = $(this).val().toLowerCase();
            $select.empty();

            originalOptions.each(function() {
                var optionText = $(this).text().toLowerCase();
                var optionValue = $(this).val().toLowerCase();

                if (optionText.includes(searchTerm) || optionValue.includes(searchTerm) || searchTerm === '') {
                    $select.append($(this).clone());
                }
            });
        });
    }

    // Apply custom search to dropdowns that don't have Select2
    $('select:not(.select2-search)').each(function() {
        if ($(this).find('option').length > 5) { // Only add search if more than 5 options
            addSearchToSelect(this);
        }
    });

    // Alternative: Convert select to input with datalist for better search
    function convertToDatalist(selectElement) {
        var $select = $(selectElement);
        var selectId = $select.attr('id');
        var selectName = $select.attr('name');
        var selectClass = $select.attr('class');
        var isRequired = $select.attr('required');

        var $input = $('<input type="text" list="' + selectId + '_list" placeholder="Type to search or select...">');
        var $datalist = $('<datalist id="' + selectId + '_list"></datalist>');

        // Copy attributes
        if (selectId) $input.attr('id', selectId);
        if (selectName) $input.attr('name', selectName);
        if (selectClass) $input.attr('class', selectClass + ' datalist-input');
        if (isRequired) $input.attr('required', 'required');

        // Copy options to datalist
        $select.find('option').each(function() {
            var $option = $(this);
            if ($option.val() !== '') {
                $datalist.append('<option value="' + $option.text() + '" data-value="' + $option.val() + '">');
            }
        });

        $select.after($input);
        $select.after($datalist);
        $select.hide(); // Hide original select but keep it for form submission

        // Handle input changes
        $input.on('change', function() {
            var inputValue = $(this).val();
            var matchedOption = $datalist.find('option[value="' + inputValue + '"]');

            if (matchedOption.length > 0) {
                $select.val(matchedOption.attr('data-value'));
            } else {
                $select.val('');
            }
        });
    }
});
</script>

<script>
// Show/hide transfer div based on dropdown selection
$(document).ready(function() {
    console.log('Transfer script loaded');
    console.log('Found shipmentType dropdown:', $('#shipmentTypeDropdown').length > 0);

    // Test if div exists
    console.log('Transfer div exists:', $('#transferDiv').length > 0);
});

// Handle shipment type radio button change
function handleShipmentTypeChange() {
    var shipmentType = $('input[name="shipmentType"]:checked').val();
    console.log('Shipment type changed to:', shipmentType);

    if (shipmentType === 'transfer') {
        console.log('Showing transfer div');
        $('#transferDiv').show();
    } else {
        console.log('Hiding transfer div');
        $('#transferDiv').hide();
        // Reset transfer form when hiding
        $('#transferTypes').val('');
        $('#transferInwardSection').hide();
        $('#transferOutwardSection').hide();
    }
}

// Transfer type change handler
function handleTransferTypeChange() {
    var transferType = $('#transferTypes').val();
    console.log('Transfer type changed to:', transferType);

    if (transferType === 'inward') {
        $('#transferInwardSection').show();
        $('#transferOutwardSection').hide();
        // Clear outward rows when switching to inward
        $('#outwardRowsContainer').empty();
        outwardRowCount = 0;
    } else if (transferType === 'outward') {
        $('#transferInwardSection').hide();
        $('#transferOutwardSection').show();
        // Clear inward rows when switching to outward
        $('#transferRowsContainer').empty();
        transferRowCount = 0;
    } else {
        $('#transferInwardSection').hide();
        $('#transferOutwardSection').hide();
        // Clear both when hiding
        $('#transferRowsContainer').empty();
        $('#outwardRowsContainer').empty();
        transferRowCount = 0;
        outwardRowCount = 0;
    }
}

// Inward row functionality
var transferRowCount = 0;

function addTransferRow() {
    transferRowCount++;
    var rowHtml = '<div class="transfer-row-flex" id="transferRow' + transferRowCount + '" style="display: flex; gap: 10px; align-items: center; margin-bottom: 10px;">' +
        '<label style="min-width: 80px;"></label>' +
        '<input type="text" name="transfer_part_no[]" placeholder="Part No" style="width: 200px;">' +
        '<input type="text" name="transfer_quantity[]" placeholder="Quantity" style="width: 200px;">' +
        '<input type="text" name="transfer_packages[]" placeholder="No. of Packages" style="width: 200px;">' +
        '<button type="button" onclick="removeTransferRow(' + transferRowCount + ')" style="background-color: red; color: white; padding: 4px 8px; border: none; border-radius: 4px; margin-left: 10px;">Remove</button>' +
        '</div>';

    $('#transferRowsContainer').append(rowHtml);
}

function removeTransferRow(rowId) {
    $('#transferRow' + rowId).remove();
}

// Outward row functionality
var outwardRowCount = 0;

function addOutwardRow() {
    outwardRowCount++;
    var rowHtml = '<div class="outward-row-flex" id="outwardRow' + outwardRowCount + '" style="display: flex; gap: 10px; align-items: center; margin-bottom: 10px;">' +
        '<label style="min-width: 80px;"></label>' +
        '<select name="outward_part_no[]" style="width: 130px;" onchange="fetchPartDetails(this.value, this)">' +
        '<option value="">-- Select Part No --</option>' +
        '</select>' +
        '<input type="text" name="existing_packages[]" placeholder="Existing Packages" style="width: 130px;" readonly>'+

            '<input type="text" name="outward_packages[]" placeholder="New Packages" style="width: 130px;">'+

            '<input type="text" name="existing_quantity[]" placeholder="Existing Quantity" style="width: 130px;" readonly>'+

       
            '<input type="text" name="outward_quantity[]" placeholder="New Quantity" style="width: 130px;">' +
    
        '<button type="button" onclick="removeOutwardRow(' + outwardRowCount + ')" style="background-color: red; color: white; padding: 4px 8px; border: none; border-radius: 4px; margin-left: -4px;">Remove</button>' +
        '</div>';

    $('#outwardRowsContainer').append(rowHtml);
}

function removeOutwardRow(rowId) {
    $('#outwardRow' + rowId).remove();
}


</script>

<script>

$("#sname").select2( {

	placeholder: "Select ",

	allowClear: true

	} );

</script>

</script>

<!-- Your existing HTML... -->






</html>



<?php

if(isset($_GET['msg']))

{

  if($_GET['msg']=='no')

  {

  echo '<script type="text/javascript"> alert(" ..! ");</script>';

  }

}



?>

<?php

if(isset($_GET['msg']))

{

  if($_GET['msg']=='yes')

  {

  echo '<script type="text/javascript"> alert("Book Shipment Sucessfully..! ");</script>';

  }

  else if($_GET['msg']=='no')

  {

  	echo '<script type="text/javascript"> alert("Problem Occured..!Booking failed, please retry OR contact admin ");</script>';

  }

}

?>


<?php

include("footer.php");

?>