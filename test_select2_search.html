<!DOCTYPE html>
<html>
<head>
    <title>Select2 Search Test</title>
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
</head>
<body>
    <h2>Select2 Search Test</h2>
    
    <label>Test Dropdown (Search anywhere in text):</label>
    <select id="testSelect" class="select2-search" style="width: 300px;">
        <option value="">-- Please Select --</option>
        <option value="1">ABC-Company Name-Mumbai</option>
        <option value="2">XYZ-Another Company-Delhi</option>
        <option value="3">DEF-Third Company-Bangalore</option>
        <option value="4">GHI-Fourth Company-Chennai</option>
        <option value="5">JKL-Fifth Company-Kolkata</option>
        <option value="6">MNO-Sixth Company-Pune</option>
        <option value="7">PQR-Seventh Company-Hyderabad</option>
    </select>

    <script>
    $(document).ready(function() {
        console.log('Testing Select2 search functionality...');
        
        // Initialize Select2 with custom matcher
        $('#testSelect').select2({
            placeholder: "-- Please Select --",
            allowClear: true,
            width: '100%',
            minimumResultsForSearch: 0,
            matcher: function(params, data) {
                // If there are no search terms, return all of the data
                if ($.trim(params.term) === '') {
                    return data;
                }

                // Skip if no text
                if (!data.text) {
                    return null;
                }

                // Check if the text contains the term (case insensitive, anywhere in string)
                if (data.text.toLowerCase().indexOf(params.term.toLowerCase()) > -1) {
                    return data;
                }

                // If it doesn't contain the term, don't return anything
                return null;
            }
        });
        
        console.log('Select2 initialized. Try searching for:');
        console.log('- "Company" (should find all companies)');
        console.log('- "Mumbai" (should find ABC company)');
        console.log('- "Third" (should find DEF company)');
        console.log('- "xyz" (should find XYZ company - case insensitive)');
    });
    </script>
</body>
</html>
