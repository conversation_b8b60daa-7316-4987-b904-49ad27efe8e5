<!DOCTYPE html>
<html>
<head>
    <title>Select2 Search Test</title>
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
</head>
<body>
    <h2>Select2 Search Test</h2>
    
    <label>Test Dropdown (Search anywhere in text):</label>
    <select id="testSelect" class="select2-search" style="width: 300px;">
        <option value="">-- Please Select --</option>
        <option value="1">ABC-Company Name-Mumbai</option>
        <option value="2">XYZ-Another Company-Delhi</option>
        <option value="3">DEF-Third Company-Bangalore</option>
        <option value="4">GHI-Fourth Company-Chennai</option>
        <option value="5">JKL-Fifth Company-Kolkata</option>
        <option value="6">MNO-Sixth Company-Pune</option>
        <option value="7">PQR-Seventh Company-Hyderabad</option>
    </select>

    <p><strong>Instructions:</strong></p>
    <ol>
        <li>Click on the dropdown above</li>
        <li>You'll see "🔍 Search..." as the second option</li>
        <li>Click on "🔍 Search..." to open the search modal</li>
        <li>Type anything to search anywhere in the text</li>
        <li>Click on any result to select it</li>
    </ol>

    <script>
    $(document).ready(function() {
        console.log('Testing SEARCH OPTION functionality...');

        var $select = $('#testSelect');

        // Store original options (excluding placeholder)
        var originalOptions = [];
        $select.find('option').each(function(index) {
            if (index > 0) { // Skip the first placeholder option
                originalOptions.push({
                    value: $(this).val(),
                    text: $(this).text(),
                    selected: $(this).is(':selected')
                });
            }
        });
        $select.data('original-options', originalOptions);

        // Add "Search" option as the second option
        var firstOption = $select.find('option:first');
        firstOption.after('<option value="__SEARCH__">🔍 Search...</option>');

        // Initialize Select2
        $select.select2({
            placeholder: "-- Please Select --",
            allowClear: true,
            width: '100%'
        });

        // Handle selection change
        $select.on('change', function() {
            var selectedValue = $(this).val();

            if (selectedValue === '__SEARCH__') {
                // Show search modal/input
                showSearchModal($select, originalOptions);

                // Reset selection to placeholder
                setTimeout(function() {
                    $select.val('').trigger('change');
                }, 100);
            }
        });

        console.log('Search option added. Click dropdown and select "🔍 Search..." option');
    });

    // Function to show search modal
    function showSearchModal($select, originalOptions) {
        var selectName = $select.attr('name') || $select.attr('id') || 'dropdown';

        // Create modal HTML
        var modalHtml = `
            <div id="searchModal" style="
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                z-index: 9999;
                display: flex;
                align-items: center;
                justify-content: center;
            ">
                <div style="
                    background: white;
                    padding: 20px;
                    border-radius: 8px;
                    width: 90%;
                    max-width: 500px;
                    max-height: 80vh;
                    overflow-y: auto;
                    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
                ">
                    <h3 style="margin-top: 0; color: #333;">Search in ${selectName}</h3>
                    <input type="text" id="searchInput" placeholder="Type to search anywhere in text..." style="
                        width: 100%;
                        padding: 10px;
                        border: 2px solid #ddd;
                        border-radius: 4px;
                        font-size: 16px;
                        margin-bottom: 15px;
                        box-sizing: border-box;
                    ">
                    <div id="searchResults" style="
                        max-height: 300px;
                        overflow-y: auto;
                        border: 1px solid #ddd;
                        border-radius: 4px;
                        margin-bottom: 15px;
                    "></div>
                    <div style="text-align: right;">
                        <button onclick="closeSearchModal()" style="
                            padding: 8px 16px;
                            background: #6c757d;
                            color: white;
                            border: none;
                            border-radius: 4px;
                            cursor: pointer;
                            margin-right: 10px;
                        ">Cancel</button>
                    </div>
                </div>
            </div>
        `;

        // Remove existing modal if any
        $('#searchModal').remove();

        // Add modal to body
        $('body').append(modalHtml);

        // Focus on search input
        $('#searchInput').focus();

        // Handle search input
        $('#searchInput').on('input', function() {
            var searchTerm = $(this).val().toLowerCase();
            var resultsHtml = '';

            if (searchTerm.length === 0) {
                resultsHtml = '<div style="padding: 10px; color: #666;">Type to search...</div>';
            } else {
                var matchCount = 0;

                $.each(originalOptions, function(index, option) {
                    if (option.value && option.text.toLowerCase().indexOf(searchTerm) > -1) {
                        resultsHtml += `
                            <div class="search-result-item" data-value="${option.value}" style="
                                padding: 10px;
                                border-bottom: 1px solid #eee;
                                cursor: pointer;
                                transition: background-color 0.2s;
                            " onmouseover="this.style.backgroundColor='#f8f9fa'"
                               onmouseout="this.style.backgroundColor='white'">
                                ${option.text}
                            </div>
                        `;
                        matchCount++;
                    }
                });

                if (matchCount === 0) {
                    resultsHtml = '<div style="padding: 10px; color: #666;">No matches found</div>';
                }
            }

            $('#searchResults').html(resultsHtml);

            // Handle result item clicks
            $('.search-result-item').on('click', function() {
                var selectedValue = $(this).data('value');
                var selectedText = $(this).text();

                // Set the selected value in the original select
                $select.val(selectedValue).trigger('change');

                console.log('Selected:', selectedText, 'with value:', selectedValue);

                // Close modal
                closeSearchModal();
            });
        });

        // Initial display
        $('#searchInput').trigger('input');
    }

    // Function to close search modal
    function closeSearchModal() {
        $('#searchModal').remove();
    }

    // Close modal when clicking outside
    $(document).on('click', '#searchModal', function(e) {
        if (e.target.id === 'searchModal') {
            closeSearchModal();
        }
    });

    // Close modal with Escape key
    $(document).on('keydown', function(e) {
        if (e.key === 'Escape') {
            closeSearchModal();
        }
    });
    </script>
</body>
</html>
