<!DOCTYPE html>
<html>
<head>
    <title>Select2 Search Test</title>
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
</head>
<body>
    <h2>Select2 Search Test</h2>
    
    <label>Test Dropdown (Search anywhere in text):</label>
    <select id="testSelect" class="select2-search" style="width: 300px;">
        <option value="">-- Please Select --</option>
        <option value="1">ABC-Company Name-Mumbai</option>
        <option value="2">XYZ-Another Company-Delhi</option>
        <option value="3">DEF-Third Company-Bangalore</option>
        <option value="4">GHI-Fourth Company-Chennai</option>
        <option value="5">JKL-Fifth Company-Kolkata</option>
        <option value="6">MNO-Sixth Company-Pune</option>
        <option value="7">PQR-Seventh Company-Hyderabad</option>
    </select>

    <script>
    $(document).ready(function() {
        console.log('Testing MANUAL Select2 search functionality...');

        var $select = $('#testSelect');

        // Store original options
        var originalOptions = [];
        $select.find('option').each(function() {
            originalOptions.push({
                value: $(this).val(),
                text: $(this).text(),
                selected: $(this).is(':selected')
            });
        });
        $select.data('original-options', originalOptions);

        // Initialize Select2 without built-in search
        $select.select2({
            placeholder: "-- Please Select --",
            allowClear: true,
            width: '100%',
            minimumResultsForSearch: Infinity // Disable built-in search
        });

        // Add manual search functionality
        $select.on('select2:open', function() {
            var $dropdown = $('.select2-dropdown');

            // Add custom search input if not already added
            if ($dropdown.find('.custom-search-input').length === 0) {
                var searchHtml = '<div style="padding: 5px; border-bottom: 1px solid #ccc; background: #f9f9f9;">' +
                                '<input type="text" class="custom-search-input" placeholder="🔍 Type to search anywhere in text..." ' +
                                'style="width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 3px; font-size: 14px;">' +
                                '</div>';
                $dropdown.prepend(searchHtml);

                // Handle search input
                $dropdown.find('.custom-search-input').on('input', function() {
                    var searchTerm = $(this).val().toLowerCase();
                    var originalOptions = $select.data('original-options');

                    console.log('Searching for:', searchTerm);

                    // Clear current options except placeholder
                    $select.find('option:not(:first)').remove();

                    // Filter and add matching options
                    var matchCount = 0;
                    $.each(originalOptions, function(index, option) {
                        if (option.value && option.text.toLowerCase().indexOf(searchTerm) > -1) {
                            var selected = option.selected ? ' selected' : '';
                            $select.append('<option value="' + option.value + '"' + selected + '>' + option.text + '</option>');
                            matchCount++;
                        }
                    });

                    console.log('Found', matchCount, 'matches for "' + searchTerm + '"');

                    // Refresh the dropdown
                    $select.select2('close').select2('open');

                    // Re-focus on search input
                    setTimeout(function() {
                        $dropdown.find('.custom-search-input').focus().val(searchTerm);
                    }, 50);
                });

                // Focus on search input
                setTimeout(function() {
                    $dropdown.find('.custom-search-input').focus();
                }, 100);
            }
        });

        console.log('Manual search initialized. Try searching for:');
        console.log('- "Company" (should find all companies)');
        console.log('- "Mumbai" (should find ABC company)');
        console.log('- "Third" (should find DEF company)');
        console.log('- "xyz" (should find XYZ company - case insensitive)');
    });
    </script>
</body>
</html>
