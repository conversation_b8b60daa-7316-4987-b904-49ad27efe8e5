<!DOCTYPE html>
<html>
<head>
    <title>Simple Search Test</title>
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
</head>
<body>
    <h2>Simple Search Test</h2>
    
    <label>Test Dropdown:</label>
    <select id="testSelect" style="width: 300px;">
        <option value="">-- Please Select --</option>
        <option value="__SEARCH__">🔍 Search...</option>
        <option value="1">ABC-Company Name-Mumbai</option>
        <option value="2">XYZ-Another Company-Delhi</option>
        <option value="3">DEF-Third Company-Bangalore</option>
        <option value="4">GHI-Fourth Company-Chennai</option>
        <option value="5">JKL-Fifth Company-Kolkata</option>
    </select>

    <br><br>
    <button onclick="testSearch()">Manual Test Search</button>

    <script>
    $(document).ready(function() {
        console.log('Simple test page loaded');
        
        var $select = $('#testSelect');
        
        // Store original options
        var originalOptions = [];
        $select.find('option').each(function() {
            var val = $(this).val();
            var text = $(this).text();
            if (val && val !== '__SEARCH__' && text !== '-- Please Select --') {
                originalOptions.push({value: val, text: text});
            }
        });
        
        console.log('Original options:', originalOptions);
        
        // Initialize Select2
        $select.select2({
            placeholder: "-- Please Select --",
            allowClear: true,
            width: '100%'
        });
        
        // Handle selection change
        $select.on('change', function() {
            var selectedValue = $(this).val();
            console.log('Selected:', selectedValue);
            
            if (selectedValue === '__SEARCH__') {
                console.log('Opening search...');
                showSearchModal($select, originalOptions);
                
                setTimeout(function() {
                    $select.val('').trigger('change');
                }, 100);
            }
        });
    });
    
    function testSearch() {
        var $select = $('#testSelect');
        var originalOptions = [
            {value: '1', text: 'ABC-Company Name-Mumbai'},
            {value: '2', text: 'XYZ-Another Company-Delhi'},
            {value: '3', text: 'DEF-Third Company-Bangalore'},
            {value: '4', text: 'GHI-Fourth Company-Chennai'},
            {value: '5', text: 'JKL-Fifth Company-Kolkata'}
        ];
        showSearchModal($select, originalOptions);
    }
    
    function showSearchModal($select, originalOptions) {
        console.log('showSearchModal called with:', originalOptions);
        
        var modalHtml = '<div id="searchModal" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.7); z-index: 9999; display: flex; align-items: center; justify-content: center;">' +
            '<div style="background: white; padding: 20px; border-radius: 8px; width: 90%; max-width: 500px; box-shadow: 0 4px 20px rgba(0,0,0,0.3);">' +
            '<h3>Search Options</h3>' +
            '<input type="text" id="searchInput" placeholder="Type to search..." style="width: 100%; padding: 10px; border: 2px solid #ddd; border-radius: 4px; margin-bottom: 15px; box-sizing: border-box;">' +
            '<div id="searchResults" style="max-height: 300px; overflow-y: auto; border: 1px solid #ddd; border-radius: 4px; margin-bottom: 15px;"></div>' +
            '<button onclick="closeSearchModal()" style="padding: 8px 16px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer;">Cancel</button>' +
            '</div></div>';
        
        $('#searchModal').remove();
        $('body').append(modalHtml);
        
        setTimeout(function() {
            $('#searchInput').focus();
        }, 100);
        
        $('#searchInput').on('input', function() {
            var searchTerm = $(this).val().toLowerCase();
            var resultsHtml = '';
            
            console.log('Searching for:', searchTerm);
            
            if (searchTerm.length === 0) {
                resultsHtml = '<div style="padding: 10px; color: #666;">Type to search...</div>';
            } else {
                var matchCount = 0;
                for (var i = 0; i < originalOptions.length; i++) {
                    var option = originalOptions[i];
                    if (option.text.toLowerCase().indexOf(searchTerm) > -1) {
                        resultsHtml += '<div class="search-result-item" data-value="' + option.value + '" style="padding: 10px; border-bottom: 1px solid #eee; cursor: pointer;">' + option.text + '</div>';
                        matchCount++;
                    }
                }
                if (matchCount === 0) {
                    resultsHtml = '<div style="padding: 10px; color: #666;">No matches found</div>';
                }
            }
            
            $('#searchResults').html(resultsHtml);
            
            $('.search-result-item').on('click', function() {
                var selectedValue = $(this).data('value');
                var selectedText = $(this).text();
                console.log('Selected:', selectedText, 'value:', selectedValue);
                $select.val(selectedValue).trigger('change');
                closeSearchModal();
            });
        });
        
        $('#searchInput').trigger('input');
    }
    
    function closeSearchModal() {
        $('#searchModal').remove();
    }
    </script>
</body>
</html>
